using System;
using System.Collections.Generic;
using System.Linq;
using System.Windows.Forms;
using ExtensionsTools.ETLogManager;
using ExtensionsTools.ETException;

namespace ExtensionsTools.ETOAAutomation.Helpers
{
    /// <summary>
    /// 上传进度监控辅助类
    /// </summary>
    public static class ETOAUploadProgressHelper
    {
        #region 私有字段
        private static readonly Dictionary<ETOAFileUploader, ETOAUploadProgressForm> _progressForms = 
            new Dictionary<ETOAFileUploader, ETOAUploadProgressForm>();
        #endregion

        #region 公共方法
        /// <summary>
        /// 显示上传进度监控窗体
        /// </summary>
        /// <param name="fileUploader">文件上传器</param>
        /// <param name="parent">父窗体</param>
        /// <returns>进度监控窗体</returns>
        public static ETOAUploadProgressForm ShowProgressForm(ETOAFileUploader fileUploader, Form parent = null)
        {
            try
            {
                if (fileUploader == null)
                    throw new ArgumentNullException(nameof(fileUploader));

                // 如果已经存在进度窗体，则显示现有的
                if (_progressForms.ContainsKey(fileUploader))
                {
                    var existingForm = _progressForms[fileUploader];
                    if (!existingForm.IsDisposed)
                    {
                        existingForm.Show();
                        existingForm.BringToFront();
                        return existingForm;
                    }
                    else
                    {
                        _progressForms.Remove(fileUploader);
                    }
                }

                // 创建新的进度窗体
                var progressForm = new ETOAUploadProgressForm(fileUploader);
                _progressForms[fileUploader] = progressForm;

                // 设置父窗体
                if (parent != null)
                {
                    progressForm.StartPosition = FormStartPosition.CenterParent;
                }

                // 处理窗体关闭事件
                progressForm.FormClosed += (sender, e) =>
                {
                    if (_progressForms.ContainsKey(fileUploader))
                    {
                        _progressForms.Remove(fileUploader);
                    }
                };

                progressForm.Show(parent);
                ETLogManager.Instance.LogInfo("ETOAUploadProgressHelper", "显示上传进度监控窗体");
                
                return progressForm;
            }
            catch (Exception ex)
            {
                ETLogManager.Instance.LogError("ETOAUploadProgressHelper", $"显示进度窗体失败: {ex.Message}");
                throw new ETException("显示进度窗体失败", ex);
            }
        }

        /// <summary>
        /// 隐藏上传进度监控窗体
        /// </summary>
        /// <param name="fileUploader">文件上传器</param>
        public static void HideProgressForm(ETOAFileUploader fileUploader)
        {
            try
            {
                if (fileUploader != null && _progressForms.ContainsKey(fileUploader))
                {
                    var progressForm = _progressForms[fileUploader];
                    if (!progressForm.IsDisposed)
                    {
                        progressForm.Hide();
                        ETLogManager.Instance.LogInfo("ETOAUploadProgressHelper", "隐藏上传进度监控窗体");
                    }
                }
            }
            catch (Exception ex)
            {
                ETLogManager.Instance.LogError("ETOAUploadProgressHelper", $"隐藏进度窗体失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 关闭上传进度监控窗体
        /// </summary>
        /// <param name="fileUploader">文件上传器</param>
        public static void CloseProgressForm(ETOAFileUploader fileUploader)
        {
            try
            {
                if (fileUploader != null && _progressForms.ContainsKey(fileUploader))
                {
                    var progressForm = _progressForms[fileUploader];
                    if (!progressForm.IsDisposed)
                    {
                        progressForm.Close();
                        ETLogManager.Instance.LogInfo("ETOAUploadProgressHelper", "关闭上传进度监控窗体");
                    }
                    _progressForms.Remove(fileUploader);
                }
            }
            catch (Exception ex)
            {
                ETLogManager.Instance.LogError("ETOAUploadProgressHelper", $"关闭进度窗体失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 获取上传进度监控窗体
        /// </summary>
        /// <param name="fileUploader">文件上传器</param>
        /// <returns>进度监控窗体，如果不存在则返回null</returns>
        public static ETOAUploadProgressForm GetProgressForm(ETOAFileUploader fileUploader)
        {
            if (fileUploader != null && _progressForms.ContainsKey(fileUploader))
            {
                var progressForm = _progressForms[fileUploader];
                return progressForm.IsDisposed ? null : progressForm;
            }
            return null;
        }

        /// <summary>
        /// 检查是否存在进度监控窗体
        /// </summary>
        /// <param name="fileUploader">文件上传器</param>
        /// <returns>是否存在</returns>
        public static bool HasProgressForm(ETOAFileUploader fileUploader)
        {
            return GetProgressForm(fileUploader) != null;
        }

        /// <summary>
        /// 关闭所有进度监控窗体
        /// </summary>
        public static void CloseAllProgressForms()
        {
            try
            {
                var formsToClose = _progressForms.Values.ToList();
                foreach (var form in formsToClose)
                {
                    if (!form.IsDisposed)
                    {
                        form.Close();
                    }
                }
                _progressForms.Clear();
                ETLogManager.Instance.LogInfo("ETOAUploadProgressHelper", "关闭所有上传进度监控窗体");
            }
            catch (Exception ex)
            {
                ETLogManager.Instance.LogError("ETOAUploadProgressHelper", $"关闭所有进度窗体失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 获取活跃的进度监控窗体数量
        /// </summary>
        /// <returns>活跃窗体数量</returns>
        public static int GetActiveProgressFormCount()
        {
            return _progressForms.Values.Count(form => !form.IsDisposed);
        }

        /// <summary>
        /// 清理已释放的进度监控窗体
        /// </summary>
        public static void CleanupDisposedForms()
        {
            try
            {
                var disposedKeys = _progressForms
                    .Where(kvp => kvp.Value.IsDisposed)
                    .Select(kvp => kvp.Key)
                    .ToList();

                foreach (var key in disposedKeys)
                {
                    _progressForms.Remove(key);
                }

                if (disposedKeys.Count > 0)
                {
                    ETLogManager.Instance.LogInfo("ETOAUploadProgressHelper", 
                        $"清理了 {disposedKeys.Count} 个已释放的进度窗体");
                }
            }
            catch (Exception ex)
            {
                ETLogManager.Instance.LogError("ETOAUploadProgressHelper", $"清理已释放窗体失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 创建简单的进度回调函数
        /// </summary>
        /// <param name="onProgress">进度回调</param>
        /// <param name="onCompleted">完成回调</param>
        /// <returns>进度回调函数</returns>
        public static Action<int> CreateProgressCallback(Action<int> onProgress = null, Action onCompleted = null)
        {
            return (progress) =>
            {
                try
                {
                    onProgress?.Invoke(progress);
                    
                    if (progress >= 100)
                    {
                        onCompleted?.Invoke();
                    }
                }
                catch (Exception ex)
                {
                    ETLogManager.Instance.LogError("ETOAUploadProgressHelper", $"进度回调执行失败: {ex.Message}");
                }
            };
        }

        /// <summary>
        /// 创建详细进度回调函数
        /// </summary>
        /// <param name="onDetailedProgress">详细进度回调</param>
        /// <param name="onCompleted">完成回调</param>
        /// <returns>详细进度回调函数</returns>
        public static Action<string, long, long> CreateDetailedProgressCallback(
            Action<string, long, long> onDetailedProgress = null, 
            Action<string> onCompleted = null)
        {
            return (fileName, uploadedBytes, totalBytes) =>
            {
                try
                {
                    onDetailedProgress?.Invoke(fileName, uploadedBytes, totalBytes);
                    
                    if (uploadedBytes >= totalBytes)
                    {
                        onCompleted?.Invoke(fileName);
                    }
                }
                catch (Exception ex)
                {
                    ETLogManager.Instance.LogError("ETOAUploadProgressHelper", $"详细进度回调执行失败: {ex.Message}");
                }
            };
        }

        /// <summary>
        /// 格式化文件大小显示
        /// </summary>
        /// <param name="bytes">字节数</param>
        /// <returns>格式化后的文件大小</returns>
        public static string FormatFileSize(long bytes)
        {
            string[] sizes = { "B", "KB", "MB", "GB", "TB" };
            double len = bytes;
            int order = 0;
            while (len >= 1024 && order < sizes.Length - 1)
            {
                order++;
                len = len / 1024;
            }
            return $"{len:0.##} {sizes[order]}";
        }

        /// <summary>
        /// 格式化上传速度显示
        /// </summary>
        /// <param name="bytesPerSecond">每秒字节数</param>
        /// <returns>格式化后的上传速度</returns>
        public static string FormatUploadSpeed(long bytesPerSecond)
        {
            if (bytesPerSecond <= 0) return "0 B/s";
            return $"{FormatFileSize(bytesPerSecond)}/s";
        }

        /// <summary>
        /// 计算剩余时间
        /// </summary>
        /// <param name="uploadedBytes">已上传字节数</param>
        /// <param name="totalBytes">总字节数</param>
        /// <param name="bytesPerSecond">每秒上传字节数</param>
        /// <returns>剩余时间字符串</returns>
        public static string CalculateRemainingTime(long uploadedBytes, long totalBytes, long bytesPerSecond)
        {
            if (bytesPerSecond <= 0 || uploadedBytes >= totalBytes)
                return "未知";

            var remainingBytes = totalBytes - uploadedBytes;
            var remainingSeconds = remainingBytes / bytesPerSecond;

            if (remainingSeconds < 60)
                return $"{remainingSeconds} 秒";
            else if (remainingSeconds < 3600)
                return $"{remainingSeconds / 60} 分钟";
            else
                return $"{remainingSeconds / 3600} 小时";
        }
        #endregion
    }
}
