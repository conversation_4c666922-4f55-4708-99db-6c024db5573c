using System;
using System.Collections.Generic;
using System.Linq;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using ET;

namespace ExtensionsTools.ETOAAutomation.Helpers
{
    /// <summary>
    /// JSON处理辅助类，提供JSON序列化、反序列化和数据处理功能
    /// </summary>
    public static class ETOAJsonHelper
    {
        #region 私有字段
        /// <summary>
        /// 默认JSON序列化设置
        /// </summary>
        private static readonly JsonSerializerSettings _defaultSettings = new JsonSerializerSettings
        {
            DateFormatHandling = DateFormatHandling.IsoDateFormat,
            NullValueHandling = NullValueHandling.Ignore,
            DefaultValueHandling = DefaultValueHandling.Include,
            Formatting = Formatting.None,
            ReferenceLoopHandling = ReferenceLoopHandling.Ignore
        };

        /// <summary>
        /// 美化输出的JSON序列化设置
        /// </summary>
        private static readonly JsonSerializerSettings _prettySettings = new JsonSerializerSettings
        {
            DateFormatHandling = DateFormatHandling.IsoDateFormat,
            NullValueHandling = NullValueHandling.Ignore,
            DefaultValueHandling = DefaultValueHandling.Include,
            Formatting = Formatting.Indented,
            ReferenceLoopHandling = ReferenceLoopHandling.Ignore
        };
        #endregion

        #region 序列化方法
        /// <summary>
        /// 将对象序列化为JSON字符串
        /// </summary>
        /// <param name="obj">要序列化的对象</param>
        /// <param name="prettyFormat">是否美化格式</param>
        /// <returns>JSON字符串</returns>
        public static string ToJson(object obj, bool prettyFormat = false)
        {
            try
            {
                if (obj == null) return "null";
                
                var settings = prettyFormat ? _prettySettings : _defaultSettings;
                return JsonConvert.SerializeObject(obj, settings);
            }
            catch (Exception ex)
            {
                ETLogManager.Error($"JSON序列化失败: {ex.Message}", ex);
                throw new ETException($"JSON序列化失败: {ex.Message}", "ToJson", ex);
            }
        }

        /// <summary>
        /// 将对象序列化为JSON字符串（安全版本，不抛出异常）
        /// </summary>
        /// <param name="obj">要序列化的对象</param>
        /// <param name="defaultValue">序列化失败时的默认值</param>
        /// <param name="prettyFormat">是否美化格式</param>
        /// <returns>JSON字符串</returns>
        public static string ToJsonSafe(object obj, string defaultValue = "{}", bool prettyFormat = false)
        {
            try
            {
                return ToJson(obj, prettyFormat);
            }
            catch (Exception ex)
            {
                ETLogManager.Warn($"JSON序列化失败，返回默认值: {ex.Message}");
                return defaultValue;
            }
        }
        #endregion

        #region 反序列化方法
        /// <summary>
        /// 将JSON字符串反序列化为指定类型的对象
        /// </summary>
        /// <typeparam name="T">目标类型</typeparam>
        /// <param name="json">JSON字符串</param>
        /// <returns>反序列化后的对象</returns>
        public static T FromJson<T>(string json)
        {
            try
            {
                if (string.IsNullOrEmpty(json)) return default(T);
                
                return JsonConvert.DeserializeObject<T>(json, _defaultSettings);
            }
            catch (Exception ex)
            {
                ETLogManager.Error($"JSON反序列化失败: {ex.Message}", ex);
                throw new ETException($"JSON反序列化失败: {ex.Message}", "FromJson", ex);
            }
        }

        /// <summary>
        /// 将JSON字符串反序列化为指定类型的对象（安全版本，不抛出异常）
        /// </summary>
        /// <typeparam name="T">目标类型</typeparam>
        /// <param name="json">JSON字符串</param>
        /// <param name="defaultValue">反序列化失败时的默认值</param>
        /// <returns>反序列化后的对象</returns>
        public static T FromJsonSafe<T>(string json, T defaultValue = default(T))
        {
            try
            {
                return FromJson<T>(json);
            }
            catch (Exception ex)
            {
                ETLogManager.Warn($"JSON反序列化失败，返回默认值: {ex.Message}");
                return defaultValue;
            }
        }

        /// <summary>
        /// 将JSON字符串反序列化为动态对象
        /// </summary>
        /// <param name="json">JSON字符串</param>
        /// <returns>动态对象</returns>
        public static dynamic FromJsonDynamic(string json)
        {
            try
            {
                if (string.IsNullOrEmpty(json)) return null;
                
                return JsonConvert.DeserializeObject(json, _defaultSettings);
            }
            catch (Exception ex)
            {
                ETLogManager.Error($"JSON动态反序列化失败: {ex.Message}", ex);
                throw new ETException($"JSON动态反序列化失败: {ex.Message}", "FromJsonDynamic", ex);
            }
        }
        #endregion

        #region JSON数据处理方法
        /// <summary>
        /// 验证JSON字符串格式是否正确
        /// </summary>
        /// <param name="json">JSON字符串</param>
        /// <returns>是否为有效JSON</returns>
        public static bool IsValidJson(string json)
        {
            if (string.IsNullOrWhiteSpace(json)) return false;
            
            try
            {
                JToken.Parse(json);
                return true;
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// 从JSON字符串中提取指定路径的值
        /// </summary>
        /// <param name="json">JSON字符串</param>
        /// <param name="path">JSON路径（如：data.items[0].name）</param>
        /// <returns>提取的值</returns>
        public static string ExtractValue(string json, string path)
        {
            try
            {
                if (string.IsNullOrEmpty(json) || string.IsNullOrEmpty(path)) return null;
                
                var jObject = JObject.Parse(json);
                var token = jObject.SelectToken(path);
                return token?.ToString();
            }
            catch (Exception ex)
            {
                ETLogManager.Warn($"JSON路径提取失败: {path}, 错误: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// 从JSON字符串中提取指定路径的强类型值
        /// </summary>
        /// <typeparam name="T">目标类型</typeparam>
        /// <param name="json">JSON字符串</param>
        /// <param name="path">JSON路径</param>
        /// <param name="defaultValue">默认值</param>
        /// <returns>提取的强类型值</returns>
        public static T ExtractValue<T>(string json, string path, T defaultValue = default(T))
        {
            try
            {
                var value = ExtractValue(json, path);
                if (value == null) return defaultValue;
                
                return (T)Convert.ChangeType(value, typeof(T));
            }
            catch (Exception ex)
            {
                ETLogManager.Warn($"JSON路径强类型提取失败: {path}, 错误: {ex.Message}");
                return defaultValue;
            }
        }

        /// <summary>
        /// 合并两个JSON对象
        /// </summary>
        /// <param name="json1">第一个JSON字符串</param>
        /// <param name="json2">第二个JSON字符串</param>
        /// <returns>合并后的JSON字符串</returns>
        public static string MergeJson(string json1, string json2)
        {
            try
            {
                if (string.IsNullOrEmpty(json1)) return json2;
                if (string.IsNullOrEmpty(json2)) return json1;
                
                var obj1 = JObject.Parse(json1);
                var obj2 = JObject.Parse(json2);
                
                obj1.Merge(obj2, new JsonMergeSettings
                {
                    MergeArrayHandling = MergeArrayHandling.Union
                });
                
                return obj1.ToString();
            }
            catch (Exception ex)
            {
                ETLogManager.Error($"JSON合并失败: {ex.Message}", ex);
                throw new ETException($"JSON合并失败: {ex.Message}", "MergeJson", ex);
            }
        }

        /// <summary>
        /// 美化JSON字符串格式
        /// </summary>
        /// <param name="json">原始JSON字符串</param>
        /// <returns>美化后的JSON字符串</returns>
        public static string PrettyFormat(string json)
        {
            try
            {
                if (string.IsNullOrEmpty(json)) return json;
                
                var obj = JsonConvert.DeserializeObject(json);
                return JsonConvert.SerializeObject(obj, _prettySettings);
            }
            catch (Exception ex)
            {
                ETLogManager.Warn($"JSON美化失败: {ex.Message}");
                return json; // 返回原始字符串
            }
        }

        /// <summary>
        /// 压缩JSON字符串（移除空白字符）
        /// </summary>
        /// <param name="json">原始JSON字符串</param>
        /// <returns>压缩后的JSON字符串</returns>
        public static string CompactFormat(string json)
        {
            try
            {
                if (string.IsNullOrEmpty(json)) return json;
                
                var obj = JsonConvert.DeserializeObject(json);
                return JsonConvert.SerializeObject(obj, _defaultSettings);
            }
            catch (Exception ex)
            {
                ETLogManager.Warn($"JSON压缩失败: {ex.Message}");
                return json; // 返回原始字符串
            }
        }
        #endregion

        #region 数据转换方法
        /// <summary>
        /// 将对象转换为字典
        /// </summary>
        /// <param name="obj">要转换的对象</param>
        /// <returns>字典对象</returns>
        public static Dictionary<string, object> ObjectToDictionary(object obj)
        {
            try
            {
                if (obj == null) return new Dictionary<string, object>();
                
                var json = ToJson(obj);
                var jObject = JObject.Parse(json);
                
                return jObject.ToObject<Dictionary<string, object>>();
            }
            catch (Exception ex)
            {
                ETLogManager.Error($"对象转字典失败: {ex.Message}", ex);
                throw new ETException($"对象转字典失败: {ex.Message}", "ObjectToDictionary", ex);
            }
        }

        /// <summary>
        /// 将字典转换为指定类型的对象
        /// </summary>
        /// <typeparam name="T">目标类型</typeparam>
        /// <param name="dictionary">字典对象</param>
        /// <returns>转换后的对象</returns>
        public static T DictionaryToObject<T>(Dictionary<string, object> dictionary)
        {
            try
            {
                if (dictionary == null) return default(T);
                
                var json = ToJson(dictionary);
                return FromJson<T>(json);
            }
            catch (Exception ex)
            {
                ETLogManager.Error($"字典转对象失败: {ex.Message}", ex);
                throw new ETException($"字典转对象失败: {ex.Message}", "DictionaryToObject", ex);
            }
        }
        #endregion
    }
}
