using System;
using System.Collections.Generic;
using System.Drawing;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using System.Windows.Forms;
using CefSharp;
using CefSharp.WinForms;
using ExtensionsTools.ETLogManager;
using ExtensionsTools.ETException;

namespace ExtensionsTools.ETOAAutomation.Helpers
{
    /// <summary>
    /// 浏览器自动化操作辅助类
    /// </summary>
    public static class ETOABrowserAutomationHelper
    {
        #region 页面操作辅助方法
        /// <summary>
        /// 等待页面完全加载
        /// </summary>
        /// <param name="browser">浏览器实例</param>
        /// <param name="timeoutMs">超时时间（毫秒）</param>
        /// <returns>是否加载完成</returns>
        public static async Task<bool> WaitForPageLoadAsync(ChromiumWebBrowser browser, int timeoutMs = 30000)
        {
            try
            {
                var startTime = DateTime.Now;
                
                while ((DateTime.Now - startTime).TotalMilliseconds < timeoutMs)
                {
                    if (!browser.IsLoading && browser.CanExecuteJavascriptInMainFrame)
                    {
                        // 检查页面是否完全加载
                        var script = "document.readyState === 'complete'";
                        var result = await browser.EvaluateScriptAsync(script);
                        
                        if (result.Success && (bool)result.Result)
                        {
                            return true;
                        }
                    }
                    
                    await Task.Delay(100);
                }
                
                return false;
            }
            catch (Exception ex)
            {
                ETLogManager.Instance.LogError("ETOABrowserAutomationHelper", $"等待页面加载失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 智能等待元素出现
        /// </summary>
        /// <param name="browser">浏览器实例</param>
        /// <param name="selector">CSS选择器</param>
        /// <param name="timeoutMs">超时时间（毫秒）</param>
        /// <param name="visible">是否要求元素可见</param>
        /// <returns>元素是否出现</returns>
        public static async Task<bool> WaitForElementAsync(ChromiumWebBrowser browser, string selector, int timeoutMs = 10000, bool visible = true)
        {
            try
            {
                var startTime = DateTime.Now;
                
                while ((DateTime.Now - startTime).TotalMilliseconds < timeoutMs)
                {
                    var script = visible 
                        ? $@"
                            var element = document.querySelector('{selector}');
                            if (element) {{
                                var rect = element.getBoundingClientRect();
                                return rect.width > 0 && rect.height > 0 && 
                                       window.getComputedStyle(element).visibility !== 'hidden' &&
                                       window.getComputedStyle(element).display !== 'none';
                            }}
                            return false;
                        "
                        : $"document.querySelector('{selector}') !== null";
                    
                    var result = await browser.EvaluateScriptAsync(script);
                    
                    if (result.Success && (bool)result.Result)
                    {
                        return true;
                    }
                    
                    await Task.Delay(100);
                }
                
                return false;
            }
            catch (Exception ex)
            {
                ETLogManager.Instance.LogError("ETOABrowserAutomationHelper", $"等待元素出现失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 智能填写表单
        /// </summary>
        /// <param name="browser">浏览器实例</param>
        /// <param name="formData">表单数据字典</param>
        /// <returns>填写是否成功</returns>
        public static async Task<bool> FillFormAsync(ChromiumWebBrowser browser, Dictionary<string, string> formData)
        {
            try
            {
                var successCount = 0;
                
                foreach (var item in formData)
                {
                    var selector = item.Key;
                    var value = item.Value;
                    
                    // 等待元素出现
                    if (await WaitForElementAsync(browser, selector, 5000))
                    {
                        var script = $@"
                            var element = document.querySelector('{selector}');
                            if (element) {{
                                element.focus();
                                element.value = '{value}';
                                element.dispatchEvent(new Event('input', {{ bubbles: true }}));
                                element.dispatchEvent(new Event('change', {{ bubbles: true }}));
                                return true;
                            }}
                            return false;
                        ";
                        
                        var result = await browser.EvaluateScriptAsync(script);
                        if (result.Success && (bool)result.Result)
                        {
                            successCount++;
                            await Task.Delay(200); // 填写间隔
                        }
                    }
                }
                
                ETLogManager.Instance.LogInfo("ETOABrowserAutomationHelper", 
                    $"表单填写完成: {successCount}/{formData.Count}");
                
                return successCount == formData.Count;
            }
            catch (Exception ex)
            {
                ETLogManager.Instance.LogError("ETOABrowserAutomationHelper", $"填写表单失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 智能点击元素（带重试）
        /// </summary>
        /// <param name="browser">浏览器实例</param>
        /// <param name="selector">CSS选择器</param>
        /// <param name="retryCount">重试次数</param>
        /// <returns>点击是否成功</returns>
        public static async Task<bool> SmartClickAsync(ChromiumWebBrowser browser, string selector, int retryCount = 3)
        {
            try
            {
                for (int i = 0; i <= retryCount; i++)
                {
                    // 等待元素出现并可见
                    if (await WaitForElementAsync(browser, selector, 5000, true))
                    {
                        // 滚动到元素位置
                        await ScrollToElementAsync(browser, selector);
                        await Task.Delay(500);
                        
                        var script = $@"
                            var element = document.querySelector('{selector}');
                            if (element) {{
                                element.scrollIntoView({{ behavior: 'smooth', block: 'center' }});
                                setTimeout(function() {{
                                    element.click();
                                }}, 100);
                                return true;
                            }}
                            return false;
                        ";
                        
                        var result = await browser.EvaluateScriptAsync(script);
                        if (result.Success && (bool)result.Result)
                        {
                            ETLogManager.Instance.LogInfo("ETOABrowserAutomationHelper", $"成功点击元素: {selector}");
                            return true;
                        }
                    }
                    
                    if (i < retryCount)
                    {
                        ETLogManager.Instance.LogWarning("ETOABrowserAutomationHelper", 
                            $"点击元素失败，重试 {i + 1}/{retryCount}: {selector}");
                        await Task.Delay(1000);
                    }
                }
                
                return false;
            }
            catch (Exception ex)
            {
                ETLogManager.Instance.LogError("ETOABrowserAutomationHelper", $"智能点击失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 滚动到指定元素
        /// </summary>
        /// <param name="browser">浏览器实例</param>
        /// <param name="selector">CSS选择器</param>
        /// <returns>滚动是否成功</returns>
        public static async Task<bool> ScrollToElementAsync(ChromiumWebBrowser browser, string selector)
        {
            try
            {
                var script = $@"
                    var element = document.querySelector('{selector}');
                    if (element) {{
                        element.scrollIntoView({{ behavior: 'smooth', block: 'center' }});
                        return true;
                    }}
                    return false;
                ";
                
                var result = await browser.EvaluateScriptAsync(script);
                return result.Success && (bool)result.Result;
            }
            catch (Exception ex)
            {
                ETLogManager.Instance.LogError("ETOABrowserAutomationHelper", $"滚动到元素失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 获取元素的位置和大小
        /// </summary>
        /// <param name="browser">浏览器实例</param>
        /// <param name="selector">CSS选择器</param>
        /// <returns>元素矩形信息</returns>
        public static async Task<Rectangle?> GetElementBoundsAsync(ChromiumWebBrowser browser, string selector)
        {
            try
            {
                var script = $@"
                    var element = document.querySelector('{selector}');
                    if (element) {{
                        var rect = element.getBoundingClientRect();
                        return {{
                            x: Math.round(rect.left),
                            y: Math.round(rect.top),
                            width: Math.round(rect.width),
                            height: Math.round(rect.height)
                        }};
                    }}
                    return null;
                ";
                
                var result = await browser.EvaluateScriptAsync(script);
                if (result.Success && result.Result != null)
                {
                    dynamic bounds = result.Result;
                    return new Rectangle((int)bounds.x, (int)bounds.y, (int)bounds.width, (int)bounds.height);
                }
                
                return null;
            }
            catch (Exception ex)
            {
                ETLogManager.Instance.LogError("ETOABrowserAutomationHelper", $"获取元素位置失败: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// 检查元素是否存在
        /// </summary>
        /// <param name="browser">浏览器实例</param>
        /// <param name="selector">CSS选择器</param>
        /// <returns>元素是否存在</returns>
        public static async Task<bool> ElementExistsAsync(ChromiumWebBrowser browser, string selector)
        {
            try
            {
                var script = $"document.querySelector('{selector}') !== null";
                var result = await browser.EvaluateScriptAsync(script);
                return result.Success && (bool)result.Result;
            }
            catch (Exception ex)
            {
                ETLogManager.Instance.LogError("ETOABrowserAutomationHelper", $"检查元素存在性失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 获取页面标题
        /// </summary>
        /// <param name="browser">浏览器实例</param>
        /// <returns>页面标题</returns>
        public static async Task<string> GetPageTitleAsync(ChromiumWebBrowser browser)
        {
            try
            {
                var result = await browser.EvaluateScriptAsync("document.title");
                return result.Success ? result.Result?.ToString() ?? "" : "";
            }
            catch (Exception ex)
            {
                ETLogManager.Instance.LogError("ETOABrowserAutomationHelper", $"获取页面标题失败: {ex.Message}");
                return "";
            }
        }

        /// <summary>
        /// 获取页面URL
        /// </summary>
        /// <param name="browser">浏览器实例</param>
        /// <returns>页面URL</returns>
        public static string GetPageUrl(ChromiumWebBrowser browser)
        {
            try
            {
                return browser.Address ?? "";
            }
            catch (Exception ex)
            {
                ETLogManager.Instance.LogError("ETOABrowserAutomationHelper", $"获取页面URL失败: {ex.Message}");
                return "";
            }
        }
        #endregion

        #region Cookie操作方法
        /// <summary>
        /// 获取所有Cookie（高级版本）
        /// </summary>
        /// <param name="browser">浏览器实例</param>
        /// <returns>Cookie字典</returns>
        public static async Task<Dictionary<string, string>> GetAllCookiesAdvancedAsync(ChromiumWebBrowser browser)
        {
            var cookies = new Dictionary<string, string>();
            
            try
            {
                var script = @"
                    var cookies = {};
                    document.cookie.split(';').forEach(function(cookie) {
                        var parts = cookie.trim().split('=');
                        if (parts.length >= 2) {
                            var name = parts[0].trim();
                            var value = parts.slice(1).join('=').trim();
                            cookies[name] = value;
                        }
                    });
                    return JSON.stringify(cookies);
                ";
                
                var result = await browser.EvaluateScriptAsync(script);
                if (result.Success && result.Result != null)
                {
                    var cookieJson = result.Result.ToString();
                    // 这里可以使用更完善的JSON解析库
                    cookies = ParseCookieJson(cookieJson);
                }
            }
            catch (Exception ex)
            {
                ETLogManager.Instance.LogError("ETOABrowserAutomationHelper", $"获取Cookie失败: {ex.Message}");
            }
            
            return cookies;
        }

        /// <summary>
        /// 设置Cookie
        /// </summary>
        /// <param name="browser">浏览器实例</param>
        /// <param name="name">Cookie名称</param>
        /// <param name="value">Cookie值</param>
        /// <param name="domain">域名</param>
        /// <param name="path">路径</param>
        /// <returns>设置是否成功</returns>
        public static async Task<bool> SetCookieAsync(ChromiumWebBrowser browser, string name, string value, string domain = null, string path = "/")
        {
            try
            {
                var cookieString = $"{name}={value}; path={path}";
                if (!string.IsNullOrEmpty(domain))
                {
                    cookieString += $"; domain={domain}";
                }
                
                var script = $"document.cookie = '{cookieString}';";
                var result = await browser.EvaluateScriptAsync(script);
                return result.Success;
            }
            catch (Exception ex)
            {
                ETLogManager.Instance.LogError("ETOABrowserAutomationHelper", $"设置Cookie失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 解析Cookie JSON字符串
        /// </summary>
        /// <param name="cookieJson">Cookie JSON字符串</param>
        /// <returns>Cookie字典</returns>
        private static Dictionary<string, string> ParseCookieJson(string cookieJson)
        {
            var result = new Dictionary<string, string>();
            
            try
            {
                // 简单的JSON解析（生产环境建议使用专业的JSON库）
                cookieJson = cookieJson.Trim('{', '}', '"');
                if (string.IsNullOrEmpty(cookieJson)) return result;
                
                var pairs = cookieJson.Split(',');
                foreach (var pair in pairs)
                {
                    var keyValue = pair.Split(':');
                    if (keyValue.Length == 2)
                    {
                        var key = keyValue[0].Trim('"', ' ');
                        var value = keyValue[1].Trim('"', ' ');
                        result[key] = value;
                    }
                }
            }
            catch (Exception ex)
            {
                ETLogManager.Instance.LogError("ETOABrowserAutomationHelper", $"解析Cookie JSON失败: {ex.Message}");
            }
            
            return result;
        }
        #endregion

        #region 实用工具方法
        /// <summary>
        /// 创建浏览器实例
        /// </summary>
        /// <param name="url">初始URL</param>
        /// <param name="userAgent">用户代理</param>
        /// <returns>浏览器实例</returns>
        public static ChromiumWebBrowser CreateBrowser(string url = "about:blank", string userAgent = null)
        {
            try
            {
                var browser = new ChromiumWebBrowser(url);
                
                if (!string.IsNullOrEmpty(userAgent))
                {
                    // 设置用户代理
                    var settings = browser.RequestContext.GetGlobalRequestContext().GetRequestContextSettings();
                    // 注意：这里需要在浏览器创建前设置，实际使用时可能需要调整
                }
                
                ETLogManager.Instance.LogInfo("ETOABrowserAutomationHelper", "创建浏览器实例成功");
                return browser;
            }
            catch (Exception ex)
            {
                ETLogManager.Instance.LogError("ETOABrowserAutomationHelper", $"创建浏览器实例失败: {ex.Message}");
                throw new ETException("创建浏览器实例失败", ex);
            }
        }

        /// <summary>
        /// 安全关闭浏览器
        /// </summary>
        /// <param name="browser">浏览器实例</param>
        public static void SafeCloseBrowser(ChromiumWebBrowser browser)
        {
            try
            {
                if (browser != null && !browser.IsDisposed)
                {
                    browser.Dispose();
                    ETLogManager.Instance.LogInfo("ETOABrowserAutomationHelper", "浏览器实例已安全关闭");
                }
            }
            catch (Exception ex)
            {
                ETLogManager.Instance.LogError("ETOABrowserAutomationHelper", $"关闭浏览器实例失败: {ex.Message}");
            }
        }
        #endregion
    }
}
