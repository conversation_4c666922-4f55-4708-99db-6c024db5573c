using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using System.Threading;
using Flurl.Http;
using ET;

namespace ExtensionsTools.ETOAAutomation.Helpers
{
    /// <summary>
    /// 重试策略辅助类，提供智能重试和容错机制
    /// </summary>
    public static class ETOARetryHelper
    {
        #region 重试策略枚举
        /// <summary>
        /// 重试策略类型
        /// </summary>
        public enum RetryStrategy
        {
            /// <summary>固定间隔</summary>
            FixedInterval,
            /// <summary>指数退避</summary>
            ExponentialBackoff,
            /// <summary>线性增长</summary>
            LinearBackoff,
            /// <summary>随机间隔</summary>
            RandomInterval
        }

        /// <summary>
        /// 错误类型
        /// </summary>
        public enum ErrorType
        {
            /// <summary>网络错误</summary>
            NetworkError,
            /// <summary>超时错误</summary>
            TimeoutError,
            /// <summary>服务器错误</summary>
            ServerError,
            /// <summary>认证错误</summary>
            AuthenticationError,
            /// <summary>限流错误</summary>
            RateLimitError,
            /// <summary>未知错误</summary>
            UnknownError
        }
        #endregion

        #region 重试配置类
        /// <summary>
        /// 重试配置
        /// </summary>
        public class RetryConfig
        {
            /// <summary>最大重试次数</summary>
            public int MaxRetryCount { get; set; } = 3;

            /// <summary>基础重试间隔（毫秒）</summary>
            public int BaseIntervalMs { get; set; } = 1000;

            /// <summary>最大重试间隔（毫秒）</summary>
            public int MaxIntervalMs { get; set; } = 30000;

            /// <summary>重试策略</summary>
            public RetryStrategy Strategy { get; set; } = RetryStrategy.ExponentialBackoff;

            /// <summary>指数退避倍数</summary>
            public double BackoffMultiplier { get; set; } = 2.0;

            /// <summary>随机因子（0-1）</summary>
            public double RandomFactor { get; set; } = 0.1;

            /// <summary>是否启用抖动</summary>
            public bool EnableJitter { get; set; } = true;

            /// <summary>可重试的错误类型</summary>
            public HashSet<ErrorType> RetryableErrors { get; set; } = new HashSet<ErrorType>
            {
                ErrorType.NetworkError,
                ErrorType.TimeoutError,
                ErrorType.ServerError,
                ErrorType.RateLimitError
            };

            /// <summary>可重试的HTTP状态码</summary>
            public HashSet<int> RetryableStatusCodes { get; set; } = new HashSet<int>
            {
                408, // Request Timeout
                429, // Too Many Requests
                500, // Internal Server Error
                502, // Bad Gateway
                503, // Service Unavailable
                504  // Gateway Timeout
            };
        }
        #endregion

        #region 重试执行方法
        /// <summary>
        /// 执行带重试的操作
        /// </summary>
        /// <typeparam name="T">返回类型</typeparam>
        /// <param name="operation">要执行的操作</param>
        /// <param name="config">重试配置</param>
        /// <param name="operationName">操作名称（用于日志）</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>操作结果</returns>
        public static async Task<T> ExecuteWithRetryAsync<T>(
            Func<Task<T>> operation,
            RetryConfig config = null,
            string operationName = "Unknown",
            CancellationToken cancellationToken = default)
        {
            config = config ?? GetDefaultRetryConfig();
            Exception lastException = null;
            
            for (int attempt = 0; attempt <= config.MaxRetryCount; attempt++)
            {
                try
                {
                    cancellationToken.ThrowIfCancellationRequested();
                    
                    ETLogManager.Info($"执行操作 '{operationName}' (尝试 {attempt + 1}/{config.MaxRetryCount + 1})");
                    var result = await operation();
                    
                    if (attempt > 0)
                    {
                        ETLogManager.Info($"操作 '{operationName}' 在第{attempt + 1}次尝试后成功");
                    }
                    
                    return result;
                }
                catch (Exception ex)
                {
                    lastException = ex;
                    var errorType = ClassifyError(ex);
                    
                    ETLogManager.Warn($"操作 '{operationName}' 第{attempt + 1}次尝试失败: {ex.Message} (错误类型: {errorType})");
                    
                    // 检查是否应该重试
                    if (attempt >= config.MaxRetryCount || !ShouldRetry(ex, config))
                    {
                        break;
                    }
                    
                    // 计算重试延迟
                    var delay = CalculateDelay(attempt, config);
                    ETLogManager.Info($"等待 {delay}ms 后重试操作 '{operationName}'");
                    
                    try
                    {
                        await Task.Delay(delay, cancellationToken);
                    }
                    catch (OperationCanceledException)
                    {
                        ETLogManager.Info($"操作 '{operationName}' 被取消");
                        throw;
                    }
                }
            }
            
            // 所有重试都失败了
            ETLogManager.Error($"操作 '{operationName}' 最终失败，已重试{config.MaxRetryCount}次", lastException);
            throw new ETException($"操作失败，已重试{config.MaxRetryCount}次: {lastException?.Message}", "ExecuteWithRetryAsync", lastException);
        }

        /// <summary>
        /// 执行带重试的无返回值操作
        /// </summary>
        /// <param name="operation">要执行的操作</param>
        /// <param name="config">重试配置</param>
        /// <param name="operationName">操作名称</param>
        /// <param name="cancellationToken">取消令牌</param>
        public static async Task ExecuteWithRetryAsync(
            Func<Task> operation,
            RetryConfig config = null,
            string operationName = "Unknown",
            CancellationToken cancellationToken = default)
        {
            await ExecuteWithRetryAsync(async () =>
            {
                await operation();
                return true; // 返回一个虚拟值
            }, config, operationName, cancellationToken);
        }
        #endregion

        #region 错误分类和判断
        /// <summary>
        /// 分类错误类型
        /// </summary>
        /// <param name="exception">异常</param>
        /// <returns>错误类型</returns>
        public static ErrorType ClassifyError(Exception exception)
        {
            switch (exception)
            {
                case FlurlHttpTimeoutException _:
                    return ErrorType.TimeoutError;
                    
                case FlurlHttpException httpEx:
                    if (httpEx.StatusCode == 401 || httpEx.StatusCode == 403)
                        return ErrorType.AuthenticationError;
                    if (httpEx.StatusCode == 429)
                        return ErrorType.RateLimitError;
                    if (httpEx.StatusCode >= 500)
                        return ErrorType.ServerError;
                    return ErrorType.NetworkError;
                    
                case TaskCanceledException _:
                case OperationCanceledException _:
                    return ErrorType.TimeoutError;
                    
                case System.Net.WebException _:
                case System.Net.Http.HttpRequestException _:
                    return ErrorType.NetworkError;
                    
                default:
                    return ErrorType.UnknownError;
            }
        }

        /// <summary>
        /// 判断是否应该重试
        /// </summary>
        /// <param name="exception">异常</param>
        /// <param name="config">重试配置</param>
        /// <returns>是否应该重试</returns>
        public static bool ShouldRetry(Exception exception, RetryConfig config)
        {
            var errorType = ClassifyError(exception);
            
            // 检查错误类型是否可重试
            if (!config.RetryableErrors.Contains(errorType))
            {
                ETLogManager.Info($"错误类型 {errorType} 不可重试");
                return false;
            }
            
            // 对于HTTP异常，检查状态码
            if (exception is FlurlHttpException httpEx)
            {
                if (!config.RetryableStatusCodes.Contains(httpEx.StatusCode ?? 0))
                {
                    ETLogManager.Info($"HTTP状态码 {httpEx.StatusCode} 不可重试");
                    return false;
                }
            }
            
            return true;
        }
        #endregion

        #region 延迟计算
        /// <summary>
        /// 计算重试延迟
        /// </summary>
        /// <param name="attemptNumber">尝试次数（从0开始）</param>
        /// <param name="config">重试配置</param>
        /// <returns>延迟时间（毫秒）</returns>
        public static int CalculateDelay(int attemptNumber, RetryConfig config)
        {
            int delay;
            
            switch (config.Strategy)
            {
                case RetryStrategy.FixedInterval:
                    delay = config.BaseIntervalMs;
                    break;
                    
                case RetryStrategy.ExponentialBackoff:
                    delay = (int)(config.BaseIntervalMs * Math.Pow(config.BackoffMultiplier, attemptNumber));
                    break;
                    
                case RetryStrategy.LinearBackoff:
                    delay = config.BaseIntervalMs * (attemptNumber + 1);
                    break;
                    
                case RetryStrategy.RandomInterval:
                    var random = new Random();
                    delay = random.Next(config.BaseIntervalMs, config.BaseIntervalMs * 3);
                    break;
                    
                default:
                    delay = config.BaseIntervalMs;
                    break;
            }
            
            // 应用最大延迟限制
            delay = Math.Min(delay, config.MaxIntervalMs);
            
            // 应用抖动
            if (config.EnableJitter)
            {
                delay = ApplyJitter(delay, config.RandomFactor);
            }
            
            return Math.Max(delay, 100); // 最小延迟100ms
        }

        /// <summary>
        /// 应用抖动
        /// </summary>
        /// <param name="delay">原始延迟</param>
        /// <param name="jitterFactor">抖动因子</param>
        /// <returns>应用抖动后的延迟</returns>
        private static int ApplyJitter(int delay, double jitterFactor)
        {
            var random = new Random();
            var jitter = delay * jitterFactor * (random.NextDouble() * 2 - 1); // -jitterFactor 到 +jitterFactor
            return (int)(delay + jitter);
        }
        #endregion

        #region 配置方法
        /// <summary>
        /// 获取默认重试配置
        /// </summary>
        /// <returns>默认重试配置</returns>
        public static RetryConfig GetDefaultRetryConfig()
        {
            return new RetryConfig
            {
                MaxRetryCount = ETOAConfigHelper.GetDefaultRetryCount(),
                BaseIntervalMs = ETOAConfigHelper.GetRetryInterval(),
                Strategy = RetryStrategy.ExponentialBackoff,
                EnableJitter = true
            };
        }

        /// <summary>
        /// 创建网络请求专用重试配置
        /// </summary>
        /// <returns>网络请求重试配置</returns>
        public static RetryConfig CreateNetworkRetryConfig()
        {
            return new RetryConfig
            {
                MaxRetryCount = 5,
                BaseIntervalMs = 1000,
                MaxIntervalMs = 30000,
                Strategy = RetryStrategy.ExponentialBackoff,
                BackoffMultiplier = 2.0,
                EnableJitter = true,
                RetryableErrors = new HashSet<ErrorType>
                {
                    ErrorType.NetworkError,
                    ErrorType.TimeoutError,
                    ErrorType.ServerError,
                    ErrorType.RateLimitError
                }
            };
        }

        /// <summary>
        /// 创建认证专用重试配置
        /// </summary>
        /// <returns>认证重试配置</returns>
        public static RetryConfig CreateAuthRetryConfig()
        {
            return new RetryConfig
            {
                MaxRetryCount = 2,
                BaseIntervalMs = 2000,
                Strategy = RetryStrategy.FixedInterval,
                EnableJitter = false,
                RetryableErrors = new HashSet<ErrorType>
                {
                    ErrorType.NetworkError,
                    ErrorType.TimeoutError
                }
            };
        }

        /// <summary>
        /// 创建快速重试配置
        /// </summary>
        /// <returns>快速重试配置</returns>
        public static RetryConfig CreateFastRetryConfig()
        {
            return new RetryConfig
            {
                MaxRetryCount = 3,
                BaseIntervalMs = 500,
                MaxIntervalMs = 5000,
                Strategy = RetryStrategy.LinearBackoff,
                EnableJitter = true
            };
        }
        #endregion

        #region 统计和监控
        /// <summary>
        /// 重试统计信息
        /// </summary>
        public class RetryStats
        {
            public int TotalAttempts { get; set; }
            public int SuccessfulRetries { get; set; }
            public int FailedRetries { get; set; }
            public TimeSpan TotalRetryTime { get; set; }
            public Dictionary<ErrorType, int> ErrorTypeCounts { get; set; } = new Dictionary<ErrorType, int>();
        }

        private static readonly Dictionary<string, RetryStats> _retryStats = new Dictionary<string, RetryStats>();
        private static readonly object _statsLock = new object();

        /// <summary>
        /// 记录重试统计
        /// </summary>
        /// <param name="operationName">操作名称</param>
        /// <param name="attempts">尝试次数</param>
        /// <param name="success">是否成功</param>
        /// <param name="totalTime">总耗时</param>
        /// <param name="errorTypes">遇到的错误类型</param>
        public static void RecordRetryStats(string operationName, int attempts, bool success, TimeSpan totalTime, IEnumerable<ErrorType> errorTypes)
        {
            lock (_statsLock)
            {
                if (!_retryStats.ContainsKey(operationName))
                {
                    _retryStats[operationName] = new RetryStats();
                }

                var stats = _retryStats[operationName];
                stats.TotalAttempts += attempts;
                stats.TotalRetryTime = stats.TotalRetryTime.Add(totalTime);

                if (success)
                {
                    stats.SuccessfulRetries++;
                }
                else
                {
                    stats.FailedRetries++;
                }

                foreach (var errorType in errorTypes)
                {
                    if (!stats.ErrorTypeCounts.ContainsKey(errorType))
                    {
                        stats.ErrorTypeCounts[errorType] = 0;
                    }
                    stats.ErrorTypeCounts[errorType]++;
                }
            }
        }

        /// <summary>
        /// 获取重试统计信息
        /// </summary>
        /// <param name="operationName">操作名称</param>
        /// <returns>统计信息</returns>
        public static RetryStats GetRetryStats(string operationName)
        {
            lock (_statsLock)
            {
                return _retryStats.ContainsKey(operationName) ? _retryStats[operationName] : new RetryStats();
            }
        }

        /// <summary>
        /// 获取所有重试统计信息
        /// </summary>
        /// <returns>所有统计信息</returns>
        public static Dictionary<string, RetryStats> GetAllRetryStats()
        {
            lock (_statsLock)
            {
                return new Dictionary<string, RetryStats>(_retryStats);
            }
        }

        /// <summary>
        /// 清除重试统计信息
        /// </summary>
        public static void ClearRetryStats()
        {
            lock (_statsLock)
            {
                _retryStats.Clear();
                ETLogManager.Info("重试统计信息已清除");
            }
        }
        #endregion
    }
}
