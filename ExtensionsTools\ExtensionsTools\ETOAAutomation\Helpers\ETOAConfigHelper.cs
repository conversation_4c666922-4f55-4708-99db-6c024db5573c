using System;
using System.IO;
using System.Windows.Forms;
using ET;

namespace ExtensionsTools.ETOAAutomation.Helpers
{
    /// <summary>
    /// 配置管理辅助类，基于ETIniFile提供OA系统配置管理功能
    /// </summary>
    public static class ETOAConfigHelper
    {
        #region 私有字段
        /// <summary>
        /// 配置文件路径
        /// </summary>
        private static readonly string _configFilePath = Path.Combine(
            Application.StartupPath, "Config", "ETOAConfig.ini");

        /// <summary>
        /// 配置文件目录
        /// </summary>
        private static readonly string _configDirectory = Path.GetDirectoryName(_configFilePath);
        #endregion

        #region 初始化方法
        /// <summary>
        /// 初始化配置文件
        /// </summary>
        public static void Initialize()
        {
            try
            {
                // 确保配置目录存在
                if (!Directory.Exists(_configDirectory))
                {
                    Directory.CreateDirectory(_configDirectory);
                    ETLogManager.Info($"创建配置目录: {_configDirectory}");
                }

                // 如果配置文件不存在，创建默认配置
                if (!File.Exists(_configFilePath))
                {
                    CreateDefaultConfig();
                    ETLogManager.Info($"创建默认配置文件: {_configFilePath}");
                }
                else
                {
                    ETLogManager.Info($"配置文件已存在: {_configFilePath}");
                }
            }
            catch (Exception ex)
            {
                ETLogManager.Error($"初始化配置文件失败: {ex.Message}", ex);
                throw new ETException($"初始化配置文件失败: {ex.Message}", "Initialize", ex);
            }
        }

        /// <summary>
        /// 创建默认配置
        /// </summary>
        private static void CreateDefaultConfig()
        {
            try
            {
                // OA系统配置
                SetOABaseUrl("https://oa.company.com");
                SetOALoginPath("/login");
                SetOAApiPath("/api");

                // 网络配置
                SetRequestTimeout(30);
                SetMaxConcurrentRequests(10);
                SetAutoRetryEnabled(true);
                SetDefaultRetryCount(3);
                SetRetryInterval(1000);

                // 缓存配置
                SetCacheEnabled(true);
                SetCacheExpirationMinutes(5);

                // 日志配置
                SetLogLevel("Info");
                SetLogToFile(true);
                SetLogMaxFileSize(10);

                // 安全配置
                SetEncryptionEnabled(true);
                SetSessionTimeout(30);

                ETLogManager.Info("默认配置创建完成");
            }
            catch (Exception ex)
            {
                ETLogManager.Error($"创建默认配置失败: {ex.Message}", ex);
                throw;
            }
        }
        #endregion

        #region OA系统配置
        /// <summary>
        /// 获取OA系统基础URL
        /// </summary>
        /// <returns>基础URL</returns>
        public static string GetOABaseUrl()
        {
            return ETIniFile.ReadString(_configFilePath, "OA", "BaseUrl", "https://oa.company.com");
        }

        /// <summary>
        /// 设置OA系统基础URL
        /// </summary>
        /// <param name="url">基础URL</param>
        public static void SetOABaseUrl(string url)
        {
            ETIniFile.WriteString(_configFilePath, "OA", "BaseUrl", url ?? "");
            ETLogManager.Info($"设置OA基础URL: {url}");
        }

        /// <summary>
        /// 获取OA登录路径
        /// </summary>
        /// <returns>登录路径</returns>
        public static string GetOALoginPath()
        {
            return ETIniFile.ReadString(_configFilePath, "OA", "LoginPath", "/login");
        }

        /// <summary>
        /// 设置OA登录路径
        /// </summary>
        /// <param name="path">登录路径</param>
        public static void SetOALoginPath(string path)
        {
            ETIniFile.WriteString(_configFilePath, "OA", "LoginPath", path ?? "/login");
            ETLogManager.Info($"设置OA登录路径: {path}");
        }

        /// <summary>
        /// 获取OA API路径
        /// </summary>
        /// <returns>API路径</returns>
        public static string GetOAApiPath()
        {
            return ETIniFile.ReadString(_configFilePath, "OA", "ApiPath", "/api");
        }

        /// <summary>
        /// 设置OA API路径
        /// </summary>
        /// <param name="path">API路径</param>
        public static void SetOAApiPath(string path)
        {
            ETIniFile.WriteString(_configFilePath, "OA", "ApiPath", path ?? "/api");
            ETLogManager.Info($"设置OA API路径: {path}");
        }

        /// <summary>
        /// 获取完整的登录URL
        /// </summary>
        /// <returns>完整登录URL</returns>
        public static string GetFullLoginUrl()
        {
            var baseUrl = GetOABaseUrl().TrimEnd('/');
            var loginPath = GetOALoginPath().TrimStart('/');
            return $"{baseUrl}/{loginPath}";
        }

        /// <summary>
        /// 获取完整的API基础URL
        /// </summary>
        /// <returns>完整API基础URL</returns>
        public static string GetFullApiBaseUrl()
        {
            var baseUrl = GetOABaseUrl().TrimEnd('/');
            var apiPath = GetOAApiPath().TrimStart('/');
            return $"{baseUrl}/{apiPath}";
        }
        #endregion

        #region 网络配置
        /// <summary>
        /// 获取请求超时时间（秒）
        /// </summary>
        /// <returns>超时时间</returns>
        public static int GetRequestTimeout()
        {
            return ETIniFile.ReadInt(_configFilePath, "Network", "TimeoutSeconds", 30);
        }

        /// <summary>
        /// 设置请求超时时间（秒）
        /// </summary>
        /// <param name="seconds">超时时间</param>
        public static void SetRequestTimeout(int seconds)
        {
            ETIniFile.WriteInt(_configFilePath, "Network", "TimeoutSeconds", seconds);
            ETLogManager.Info($"设置请求超时时间: {seconds}秒");
        }

        /// <summary>
        /// 获取最大并发请求数
        /// </summary>
        /// <returns>最大并发请求数</returns>
        public static int GetMaxConcurrentRequests()
        {
            return ETIniFile.ReadInt(_configFilePath, "Network", "MaxConcurrentRequests", 10);
        }

        /// <summary>
        /// 设置最大并发请求数
        /// </summary>
        /// <param name="count">最大并发请求数</param>
        public static void SetMaxConcurrentRequests(int count)
        {
            ETIniFile.WriteInt(_configFilePath, "Network", "MaxConcurrentRequests", count);
            ETLogManager.Info($"设置最大并发请求数: {count}");
        }

        /// <summary>
        /// 获取是否启用自动重试
        /// </summary>
        /// <returns>是否启用自动重试</returns>
        public static bool GetAutoRetryEnabled()
        {
            return ETIniFile.ReadBool(_configFilePath, "Network", "AutoRetry", true);
        }

        /// <summary>
        /// 设置是否启用自动重试
        /// </summary>
        /// <param name="enabled">是否启用</param>
        public static void SetAutoRetryEnabled(bool enabled)
        {
            ETIniFile.WriteBool(_configFilePath, "Network", "AutoRetry", enabled);
            ETLogManager.Info($"设置自动重试: {enabled}");
        }

        /// <summary>
        /// 获取默认重试次数
        /// </summary>
        /// <returns>默认重试次数</returns>
        public static int GetDefaultRetryCount()
        {
            return ETIniFile.ReadInt(_configFilePath, "Network", "DefaultRetryCount", 3);
        }

        /// <summary>
        /// 设置默认重试次数
        /// </summary>
        /// <param name="count">重试次数</param>
        public static void SetDefaultRetryCount(int count)
        {
            ETIniFile.WriteInt(_configFilePath, "Network", "DefaultRetryCount", count);
            ETLogManager.Info($"设置默认重试次数: {count}");
        }

        /// <summary>
        /// 获取重试间隔（毫秒）
        /// </summary>
        /// <returns>重试间隔</returns>
        public static int GetRetryInterval()
        {
            return ETIniFile.ReadInt(_configFilePath, "Network", "RetryIntervalMs", 1000);
        }

        /// <summary>
        /// 设置重试间隔（毫秒）
        /// </summary>
        /// <param name="intervalMs">重试间隔</param>
        public static void SetRetryInterval(int intervalMs)
        {
            ETIniFile.WriteInt(_configFilePath, "Network", "RetryIntervalMs", intervalMs);
            ETLogManager.Info($"设置重试间隔: {intervalMs}毫秒");
        }
        #endregion

        #region 缓存配置
        /// <summary>
        /// 获取是否启用缓存
        /// </summary>
        /// <returns>是否启用缓存</returns>
        public static bool GetCacheEnabled()
        {
            return ETIniFile.ReadBool(_configFilePath, "Cache", "Enabled", true);
        }

        /// <summary>
        /// 设置是否启用缓存
        /// </summary>
        /// <param name="enabled">是否启用</param>
        public static void SetCacheEnabled(bool enabled)
        {
            ETIniFile.WriteBool(_configFilePath, "Cache", "Enabled", enabled);
            ETLogManager.Info($"设置缓存启用状态: {enabled}");
        }

        /// <summary>
        /// 获取缓存过期时间（分钟）
        /// </summary>
        /// <returns>缓存过期时间</returns>
        public static int GetCacheExpirationMinutes()
        {
            return ETIniFile.ReadInt(_configFilePath, "Cache", "ExpirationMinutes", 5);
        }

        /// <summary>
        /// 设置缓存过期时间（分钟）
        /// </summary>
        /// <param name="minutes">过期时间</param>
        public static void SetCacheExpirationMinutes(int minutes)
        {
            ETIniFile.WriteInt(_configFilePath, "Cache", "ExpirationMinutes", minutes);
            ETLogManager.Info($"设置缓存过期时间: {minutes}分钟");
        }
        #endregion

        #region 日志配置
        /// <summary>
        /// 获取日志级别
        /// </summary>
        /// <returns>日志级别</returns>
        public static string GetLogLevel()
        {
            return ETIniFile.ReadString(_configFilePath, "Log", "Level", "Info");
        }

        /// <summary>
        /// 设置日志级别
        /// </summary>
        /// <param name="level">日志级别</param>
        public static void SetLogLevel(string level)
        {
            ETIniFile.WriteString(_configFilePath, "Log", "Level", level ?? "Info");
            ETLogManager.Info($"设置日志级别: {level}");
        }

        /// <summary>
        /// 获取是否记录到文件
        /// </summary>
        /// <returns>是否记录到文件</returns>
        public static bool GetLogToFile()
        {
            return ETIniFile.ReadBool(_configFilePath, "Log", "ToFile", true);
        }

        /// <summary>
        /// 设置是否记录到文件
        /// </summary>
        /// <param name="toFile">是否记录到文件</param>
        public static void SetLogToFile(bool toFile)
        {
            ETIniFile.WriteBool(_configFilePath, "Log", "ToFile", toFile);
            ETLogManager.Info($"设置日志记录到文件: {toFile}");
        }

        /// <summary>
        /// 获取日志文件最大大小（MB）
        /// </summary>
        /// <returns>最大大小</returns>
        public static int GetLogMaxFileSize()
        {
            return ETIniFile.ReadInt(_configFilePath, "Log", "MaxFileSizeMB", 10);
        }

        /// <summary>
        /// 设置日志文件最大大小（MB）
        /// </summary>
        /// <param name="sizeMB">最大大小</param>
        public static void SetLogMaxFileSize(int sizeMB)
        {
            ETIniFile.WriteInt(_configFilePath, "Log", "MaxFileSizeMB", sizeMB);
            ETLogManager.Info($"设置日志文件最大大小: {sizeMB}MB");
        }
        #endregion

        #region 安全配置
        /// <summary>
        /// 获取是否启用加密
        /// </summary>
        /// <returns>是否启用加密</returns>
        public static bool GetEncryptionEnabled()
        {
            return ETIniFile.ReadBool(_configFilePath, "Security", "EncryptionEnabled", true);
        }

        /// <summary>
        /// 设置是否启用加密
        /// </summary>
        /// <param name="enabled">是否启用</param>
        public static void SetEncryptionEnabled(bool enabled)
        {
            ETIniFile.WriteBool(_configFilePath, "Security", "EncryptionEnabled", enabled);
            ETLogManager.Info($"设置加密启用状态: {enabled}");
        }

        /// <summary>
        /// 获取会话超时时间（分钟）
        /// </summary>
        /// <returns>会话超时时间</returns>
        public static int GetSessionTimeout()
        {
            return ETIniFile.ReadInt(_configFilePath, "Security", "SessionTimeoutMinutes", 30);
        }

        /// <summary>
        /// 设置会话超时时间（分钟）
        /// </summary>
        /// <param name="minutes">超时时间</param>
        public static void SetSessionTimeout(int minutes)
        {
            ETIniFile.WriteInt(_configFilePath, "Security", "SessionTimeoutMinutes", minutes);
            ETLogManager.Info($"设置会话超时时间: {minutes}分钟");
        }
        #endregion

        #region 通用配置方法
        /// <summary>
        /// 获取配置值
        /// </summary>
        /// <param name="section">配置节</param>
        /// <param name="key">配置键</param>
        /// <param name="defaultValue">默认值</param>
        /// <returns>配置值</returns>
        public static string GetConfig(string section, string key, string defaultValue = "")
        {
            return ETIniFile.ReadString(_configFilePath, section, key, defaultValue);
        }

        /// <summary>
        /// 设置配置值
        /// </summary>
        /// <param name="section">配置节</param>
        /// <param name="key">配置键</param>
        /// <param name="value">配置值</param>
        public static void SetConfig(string section, string key, string value)
        {
            ETIniFile.WriteString(_configFilePath, section, key, value ?? "");
            ETLogManager.Info($"设置配置 [{section}]{key} = {value}");
        }

        /// <summary>
        /// 获取配置文件路径
        /// </summary>
        /// <returns>配置文件路径</returns>
        public static string GetConfigFilePath()
        {
            return _configFilePath;
        }

        /// <summary>
        /// 检查配置文件是否存在
        /// </summary>
        /// <returns>是否存在</returns>
        public static bool ConfigFileExists()
        {
            return File.Exists(_configFilePath);
        }

        /// <summary>
        /// 重置配置为默认值
        /// </summary>
        public static void ResetToDefault()
        {
            try
            {
                if (File.Exists(_configFilePath))
                {
                    File.Delete(_configFilePath);
                    ETLogManager.Info("删除现有配置文件");
                }

                CreateDefaultConfig();
                ETLogManager.Info("配置已重置为默认值");
            }
            catch (Exception ex)
            {
                ETLogManager.Error($"重置配置失败: {ex.Message}", ex);
                throw new ETException($"重置配置失败: {ex.Message}", "ResetToDefault", ex);
            }
        }
        #endregion
    }
}
