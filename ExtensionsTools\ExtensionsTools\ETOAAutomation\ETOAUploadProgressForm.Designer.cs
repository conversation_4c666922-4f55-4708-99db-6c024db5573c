namespace ExtensionsTools.ETOAAutomation
{
    partial class ETOAUploadProgressForm
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.PanelMain = new System.Windows.Forms.Panel();
            this.PanelContent = new System.Windows.Forms.Panel();
            this.LvUploads = new System.Windows.Forms.ListView();
            this.ColFileName = ((System.Windows.Forms.ColumnHeader)(new System.Windows.Forms.ColumnHeader()));
            this.ColFileSize = ((System.Windows.Forms.ColumnHeader)(new System.Windows.Forms.ColumnHeader()));
            this.ColProgress = ((System.Windows.Forms.ColumnHeader)(new System.Windows.Forms.ColumnHeader()));
            this.ColSpeed = ((System.Windows.Forms.ColumnHeader)(new System.Windows.Forms.ColumnHeader()));
            this.ColStatus = ((System.Windows.Forms.ColumnHeader)(new System.Windows.Forms.ColumnHeader()));
            this.PanelButtons = new System.Windows.Forms.Panel();
            this.BtnCancelAll = new System.Windows.Forms.Button();
            this.BtnClearCompleted = new System.Windows.Forms.Button();
            this.BtnCancel = new System.Windows.Forms.Button();
            this.BtnPause = new System.Windows.Forms.Button();
            this.PanelStats = new System.Windows.Forms.Panel();
            this.GroupBoxStats = new System.Windows.Forms.GroupBox();
            this.LblAverageSpeed = new System.Windows.Forms.Label();
            this.LblOverallProgress = new System.Windows.Forms.Label();
            this.LblUploadedSize = new System.Windows.Forms.Label();
            this.LblTotalSize = new System.Windows.Forms.Label();
            this.LblActiveFiles = new System.Windows.Forms.Label();
            this.LblCompletedFiles = new System.Windows.Forms.Label();
            this.LblTotalFiles = new System.Windows.Forms.Label();
            this.PanelProgressBar = new System.Windows.Forms.Panel();
            this.ProgressBarOverall = new System.Windows.Forms.ProgressBar();
            this.LblOverallTitle = new System.Windows.Forms.Label();
            this.PanelMain.SuspendLayout();
            this.PanelContent.SuspendLayout();
            this.PanelButtons.SuspendLayout();
            this.PanelStats.SuspendLayout();
            this.GroupBoxStats.SuspendLayout();
            this.PanelProgressBar.SuspendLayout();
            this.SuspendLayout();
            // 
            // PanelMain
            // 
            this.PanelMain.Controls.Add(this.PanelContent);
            this.PanelMain.Controls.Add(this.PanelButtons);
            this.PanelMain.Controls.Add(this.PanelStats);
            this.PanelMain.Controls.Add(this.PanelProgressBar);
            this.PanelMain.Dock = System.Windows.Forms.DockStyle.Fill;
            this.PanelMain.Location = new System.Drawing.Point(0, 0);
            this.PanelMain.Name = "PanelMain";
            this.PanelMain.Size = new System.Drawing.Size(800, 600);
            this.PanelMain.TabIndex = 0;
            // 
            // PanelContent
            // 
            this.PanelContent.Controls.Add(this.LvUploads);
            this.PanelContent.Dock = System.Windows.Forms.DockStyle.Fill;
            this.PanelContent.Location = new System.Drawing.Point(0, 0);
            this.PanelContent.Name = "PanelContent";
            this.PanelContent.Padding = new System.Windows.Forms.Padding(10);
            this.PanelContent.Size = new System.Drawing.Size(800, 420);
            this.PanelContent.TabIndex = 0;
            // 
            // LvUploads
            // 
            this.LvUploads.Columns.AddRange(new System.Windows.Forms.ColumnHeader[] {
            this.ColFileName,
            this.ColFileSize,
            this.ColProgress,
            this.ColSpeed,
            this.ColStatus});
            this.LvUploads.Dock = System.Windows.Forms.DockStyle.Fill;
            this.LvUploads.FullRowSelect = true;
            this.LvUploads.GridLines = true;
            this.LvUploads.HideSelection = false;
            this.LvUploads.Location = new System.Drawing.Point(10, 10);
            this.LvUploads.MultiSelect = false;
            this.LvUploads.Name = "LvUploads";
            this.LvUploads.Size = new System.Drawing.Size(780, 400);
            this.LvUploads.TabIndex = 0;
            this.LvUploads.UseCompatibleStateImageBehavior = false;
            this.LvUploads.View = System.Windows.Forms.View.Details;
            // 
            // ColFileName
            // 
            this.ColFileName.Text = "文件名";
            this.ColFileName.Width = 250;
            // 
            // ColFileSize
            // 
            this.ColFileSize.Text = "文件大小";
            this.ColFileSize.Width = 100;
            // 
            // ColProgress
            // 
            this.ColProgress.Text = "进度";
            this.ColProgress.Width = 80;
            // 
            // ColSpeed
            // 
            this.ColSpeed.Text = "速度";
            this.ColSpeed.Width = 120;
            // 
            // ColStatus
            // 
            this.ColStatus.Text = "状态";
            this.ColStatus.Width = 100;
            // 
            // PanelButtons
            // 
            this.PanelButtons.Controls.Add(this.BtnCancelAll);
            this.PanelButtons.Controls.Add(this.BtnClearCompleted);
            this.PanelButtons.Controls.Add(this.BtnCancel);
            this.PanelButtons.Controls.Add(this.BtnPause);
            this.PanelButtons.Dock = System.Windows.Forms.DockStyle.Bottom;
            this.PanelButtons.Location = new System.Drawing.Point(0, 550);
            this.PanelButtons.Name = "PanelButtons";
            this.PanelButtons.Padding = new System.Windows.Forms.Padding(10);
            this.PanelButtons.Size = new System.Drawing.Size(800, 50);
            this.PanelButtons.TabIndex = 1;
            // 
            // BtnCancelAll
            // 
            this.BtnCancelAll.Location = new System.Drawing.Point(330, 13);
            this.BtnCancelAll.Name = "BtnCancelAll";
            this.BtnCancelAll.Size = new System.Drawing.Size(100, 30);
            this.BtnCancelAll.TabIndex = 3;
            this.BtnCancelAll.Text = "全部取消";
            this.BtnCancelAll.UseVisualStyleBackColor = true;
            this.BtnCancelAll.Click += new System.EventHandler(this.BtnCancelAll_Click);
            // 
            // BtnClearCompleted
            // 
            this.BtnClearCompleted.Location = new System.Drawing.Point(220, 13);
            this.BtnClearCompleted.Name = "BtnClearCompleted";
            this.BtnClearCompleted.Size = new System.Drawing.Size(100, 30);
            this.BtnClearCompleted.TabIndex = 2;
            this.BtnClearCompleted.Text = "清除已完成";
            this.BtnClearCompleted.UseVisualStyleBackColor = true;
            this.BtnClearCompleted.Click += new System.EventHandler(this.BtnClearCompleted_Click);
            // 
            // BtnCancel
            // 
            this.BtnCancel.Location = new System.Drawing.Point(110, 13);
            this.BtnCancel.Name = "BtnCancel";
            this.BtnCancel.Size = new System.Drawing.Size(100, 30);
            this.BtnCancel.TabIndex = 1;
            this.BtnCancel.Text = "取消上传";
            this.BtnCancel.UseVisualStyleBackColor = true;
            this.BtnCancel.Click += new System.EventHandler(this.BtnCancel_Click);
            // 
            // BtnPause
            // 
            this.BtnPause.Location = new System.Drawing.Point(10, 13);
            this.BtnPause.Name = "BtnPause";
            this.BtnPause.Size = new System.Drawing.Size(100, 30);
            this.BtnPause.TabIndex = 0;
            this.BtnPause.Text = "暂停/恢复";
            this.BtnPause.UseVisualStyleBackColor = true;
            this.BtnPause.Click += new System.EventHandler(this.BtnPause_Click);
            // 
            // PanelStats
            // 
            this.PanelStats.Controls.Add(this.GroupBoxStats);
            this.PanelStats.Dock = System.Windows.Forms.DockStyle.Bottom;
            this.PanelStats.Location = new System.Drawing.Point(0, 470);
            this.PanelStats.Name = "PanelStats";
            this.PanelStats.Padding = new System.Windows.Forms.Padding(10);
            this.PanelStats.Size = new System.Drawing.Size(800, 80);
            this.PanelStats.TabIndex = 2;
            // 
            // GroupBoxStats
            // 
            this.GroupBoxStats.Controls.Add(this.LblAverageSpeed);
            this.GroupBoxStats.Controls.Add(this.LblOverallProgress);
            this.GroupBoxStats.Controls.Add(this.LblUploadedSize);
            this.GroupBoxStats.Controls.Add(this.LblTotalSize);
            this.GroupBoxStats.Controls.Add(this.LblActiveFiles);
            this.GroupBoxStats.Controls.Add(this.LblCompletedFiles);
            this.GroupBoxStats.Controls.Add(this.LblTotalFiles);
            this.GroupBoxStats.Dock = System.Windows.Forms.DockStyle.Fill;
            this.GroupBoxStats.Location = new System.Drawing.Point(10, 10);
            this.GroupBoxStats.Name = "GroupBoxStats";
            this.GroupBoxStats.Size = new System.Drawing.Size(780, 60);
            this.GroupBoxStats.TabIndex = 0;
            this.GroupBoxStats.TabStop = false;
            this.GroupBoxStats.Text = "统计信息";
            // 
            // LblAverageSpeed
            // 
            this.LblAverageSpeed.AutoSize = true;
            this.LblAverageSpeed.Location = new System.Drawing.Point(580, 35);
            this.LblAverageSpeed.Name = "LblAverageSpeed";
            this.LblAverageSpeed.Size = new System.Drawing.Size(89, 12);
            this.LblAverageSpeed.TabIndex = 6;
            this.LblAverageSpeed.Text = "平均速度: 0 B/s";
            // 
            // LblOverallProgress
            // 
            this.LblOverallProgress.AutoSize = true;
            this.LblOverallProgress.Location = new System.Drawing.Point(480, 35);
            this.LblOverallProgress.Name = "LblOverallProgress";
            this.LblOverallProgress.Size = new System.Drawing.Size(65, 12);
            this.LblOverallProgress.TabIndex = 5;
            this.LblOverallProgress.Text = "总进度: 0%";
            // 
            // LblUploadedSize
            // 
            this.LblUploadedSize.AutoSize = true;
            this.LblUploadedSize.Location = new System.Drawing.Point(380, 35);
            this.LblUploadedSize.Name = "LblUploadedSize";
            this.LblUploadedSize.Size = new System.Drawing.Size(71, 12);
            this.LblUploadedSize.TabIndex = 4;
            this.LblUploadedSize.Text = "已上传: 0 B";
            // 
            // LblTotalSize
            // 
            this.LblTotalSize.AutoSize = true;
            this.LblTotalSize.Location = new System.Drawing.Point(280, 35);
            this.LblTotalSize.Name = "LblTotalSize";
            this.LblTotalSize.Size = new System.Drawing.Size(71, 12);
            this.LblTotalSize.TabIndex = 3;
            this.LblTotalSize.Text = "总大小: 0 B";
            // 
            // LblActiveFiles
            // 
            this.LblActiveFiles.AutoSize = true;
            this.LblActiveFiles.Location = new System.Drawing.Point(180, 35);
            this.LblActiveFiles.Name = "LblActiveFiles";
            this.LblActiveFiles.Size = new System.Drawing.Size(59, 12);
            this.LblActiveFiles.TabIndex = 2;
            this.LblActiveFiles.Text = "进行中: 0";
            // 
            // LblCompletedFiles
            // 
            this.LblCompletedFiles.AutoSize = true;
            this.LblCompletedFiles.Location = new System.Drawing.Point(90, 35);
            this.LblCompletedFiles.Name = "LblCompletedFiles";
            this.LblCompletedFiles.Size = new System.Drawing.Size(59, 12);
            this.LblCompletedFiles.TabIndex = 1;
            this.LblCompletedFiles.Text = "已完成: 0";
            // 
            // LblTotalFiles
            // 
            this.LblTotalFiles.AutoSize = true;
            this.LblTotalFiles.Location = new System.Drawing.Point(10, 35);
            this.LblTotalFiles.Name = "LblTotalFiles";
            this.LblTotalFiles.Size = new System.Drawing.Size(71, 12);
            this.LblTotalFiles.TabIndex = 0;
            this.LblTotalFiles.Text = "总文件数: 0";
            // 
            // PanelProgressBar
            // 
            this.PanelProgressBar.Controls.Add(this.ProgressBarOverall);
            this.PanelProgressBar.Controls.Add(this.LblOverallTitle);
            this.PanelProgressBar.Dock = System.Windows.Forms.DockStyle.Bottom;
            this.PanelProgressBar.Location = new System.Drawing.Point(0, 420);
            this.PanelProgressBar.Name = "PanelProgressBar";
            this.PanelProgressBar.Padding = new System.Windows.Forms.Padding(10);
            this.PanelProgressBar.Size = new System.Drawing.Size(800, 50);
            this.PanelProgressBar.TabIndex = 3;
            // 
            // ProgressBarOverall
            // 
            this.ProgressBarOverall.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.ProgressBarOverall.Location = new System.Drawing.Point(80, 15);
            this.ProgressBarOverall.Name = "ProgressBarOverall";
            this.ProgressBarOverall.Size = new System.Drawing.Size(710, 20);
            this.ProgressBarOverall.TabIndex = 1;
            // 
            // LblOverallTitle
            // 
            this.LblOverallTitle.AutoSize = true;
            this.LblOverallTitle.Location = new System.Drawing.Point(10, 18);
            this.LblOverallTitle.Name = "LblOverallTitle";
            this.LblOverallTitle.Size = new System.Drawing.Size(65, 12);
            this.LblOverallTitle.TabIndex = 0;
            this.LblOverallTitle.Text = "总体进度:";
            // 
            // ETOAUploadProgressForm
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 12F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(800, 600);
            this.Controls.Add(this.PanelMain);
            this.MinimumSize = new System.Drawing.Size(600, 400);
            this.Name = "ETOAUploadProgressForm";
            this.StartPosition = System.Windows.Forms.FormStartPosition.CenterParent;
            this.Text = "文件上传进度监控";
            this.PanelMain.ResumeLayout(false);
            this.PanelContent.ResumeLayout(false);
            this.PanelButtons.ResumeLayout(false);
            this.PanelStats.ResumeLayout(false);
            this.GroupBoxStats.ResumeLayout(false);
            this.GroupBoxStats.PerformLayout();
            this.PanelProgressBar.ResumeLayout(false);
            this.PanelProgressBar.PerformLayout();
            this.ResumeLayout(false);

        }

        #endregion

        private System.Windows.Forms.Panel PanelMain;
        private System.Windows.Forms.Panel PanelContent;
        private System.Windows.Forms.ListView LvUploads;
        private System.Windows.Forms.ColumnHeader ColFileName;
        private System.Windows.Forms.ColumnHeader ColFileSize;
        private System.Windows.Forms.ColumnHeader ColProgress;
        private System.Windows.Forms.ColumnHeader ColSpeed;
        private System.Windows.Forms.ColumnHeader ColStatus;
        private System.Windows.Forms.Panel PanelButtons;
        private System.Windows.Forms.Button BtnCancelAll;
        private System.Windows.Forms.Button BtnClearCompleted;
        private System.Windows.Forms.Button BtnCancel;
        private System.Windows.Forms.Button BtnPause;
        private System.Windows.Forms.Panel PanelStats;
        private System.Windows.Forms.GroupBox GroupBoxStats;
        private System.Windows.Forms.Label LblAverageSpeed;
        private System.Windows.Forms.Label LblOverallProgress;
        private System.Windows.Forms.Label LblUploadedSize;
        private System.Windows.Forms.Label LblTotalSize;
        private System.Windows.Forms.Label LblActiveFiles;
        private System.Windows.Forms.Label LblCompletedFiles;
        private System.Windows.Forms.Label LblTotalFiles;
        private System.Windows.Forms.Panel PanelProgressBar;
        private System.Windows.Forms.ProgressBar ProgressBarOverall;
        private System.Windows.Forms.Label LblOverallTitle;
    }
}
