﻿2025-08-02 08:29:48 [INFO] 显示设置已变更
2025-08-02 08:29:49 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-08-02 08:29:50 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 111876918
2025-08-02 08:29:50 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 111876918)
2025-08-02 08:29:50 [INFO] TopMostForm.ForceRestoreParentRelation: 强制恢复完成
2025-08-02 08:29:50 [INFO] 显示设置变更后TopForm关系已重建
2025-08-02 08:33:58 [INFO] TopMostForm.Start: 开始启动窗体管理功能
2025-08-02 08:33:58 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 111876918, 新父窗口: 7742166
2025-08-02 08:33:58 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 7742166)
2025-08-02 08:33:58 [INFO] TopMostForm.Start: 窗体管理功能启动完成
2025-08-02 08:33:58 [INFO] App_WorkbookActivate: 工作簿 '一点一案上传方案信息表-20250801-170610.xlsx' 激活处理完成
2025-08-02 08:33:58 [INFO] TopMostForm.Start: 开始启动窗体管理功能
2025-08-02 08:33:58 [INFO] SetTop: 父子窗口关系已正确 (Excel句柄: 7742166)
2025-08-02 08:33:58 [INFO] TopMostForm.Start: 窗体管理功能启动完成
2025-08-02 08:33:58 [INFO] App_WindowActivate: Excel窗口激活处理完成
2025-08-02 08:33:58 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-08-02 08:33:58 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 7742166
2025-08-02 08:33:58 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 7742166)
2025-08-02 08:33:58 [INFO] TopMostForm.ForceRestoreParentRelation: 强制恢复完成
2025-08-02 08:33:58 [INFO] App_WorkbookActivate: TopForm关系验证完成
2025-08-02 08:33:58 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-08-02 08:33:58 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 7742166
2025-08-02 08:33:58 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 7742166)
2025-08-02 08:33:58 [INFO] TopMostForm.ForceRestoreParentRelation: 强制恢复完成
2025-08-02 08:35:29 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-08-02 08:35:30 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 7742166
2025-08-02 08:35:30 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 7742166)
2025-08-02 08:35:30 [INFO] TopMostForm.ForceRestoreParentRelation: 强制恢复完成
2025-08-02 08:35:30 [INFO] Application_WindowResize: 已重新验证TopForm父子关系
2025-08-02 08:35:30 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-08-02 08:35:30 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 7742166
2025-08-02 08:35:30 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 7742166)
2025-08-02 08:35:30 [INFO] TopMostForm.ForceRestoreParentRelation: 强制恢复完成
2025-08-02 08:35:30 [INFO] Application_WindowResize: 已重新验证TopForm父子关系
2025-08-02 09:36:18 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-08-02 09:36:18 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 7742166
2025-08-02 09:36:18 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 7742166)
2025-08-02 09:36:18 [INFO] TopMostForm.ForceRestoreParentRelation: 强制恢复完成
2025-08-02 09:36:18 [INFO] Application_WindowResize: 已重新验证TopForm父子关系
2025-08-02 09:36:18 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-08-02 09:36:18 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 111876918
2025-08-02 09:36:18 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 111876918)
2025-08-02 09:36:18 [INFO] TopMostForm.ForceRestoreParentRelation: 强制恢复完成
2025-08-02 09:36:18 [INFO] Application_WindowResize: 已重新验证TopForm父子关系
2025-08-02 09:36:18 [INFO] TopMostForm.Start: 开始启动窗体管理功能
2025-08-02 09:36:18 [INFO] SetTop: 父子窗口关系已正确 (Excel句柄: 111876918)
2025-08-02 09:36:18 [INFO] TopMostForm.Start: 窗体管理功能启动完成
2025-08-02 09:36:18 [INFO] App_WorkbookActivate: 工作簿 '场景清单_专用场景_ad60d545-8ea4-4b24-bd64-e2b8352ed1a1.csv' 激活处理完成
2025-08-02 09:36:18 [INFO] TopMostForm.Start: 开始启动窗体管理功能
2025-08-02 09:36:18 [INFO] SetTop: 父子窗口关系已正确 (Excel句柄: 111876918)
2025-08-02 09:36:18 [INFO] TopMostForm.Start: 窗体管理功能启动完成
2025-08-02 09:36:18 [INFO] App_WindowActivate: Excel窗口激活处理完成
2025-08-02 09:36:19 [INFO] TopMostForm.Start: 开始启动窗体管理功能
2025-08-02 09:36:19 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 111876918, 新父窗口: 7742166
2025-08-02 09:36:19 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 7742166)
2025-08-02 09:36:19 [INFO] TopMostForm.Start: 窗体管理功能启动完成
2025-08-02 09:36:19 [INFO] App_WorkbookActivate: 工作簿 '一点一案上传方案信息表-20250801-170610.xlsx' 激活处理完成
2025-08-02 09:36:19 [INFO] TopMostForm.Start: 开始启动窗体管理功能
2025-08-02 09:36:19 [INFO] SetTop: 父子窗口关系已正确 (Excel句柄: 7742166)
2025-08-02 09:36:19 [INFO] TopMostForm.Start: 窗体管理功能启动完成
2025-08-02 09:36:19 [INFO] App_WindowActivate: Excel窗口激活处理完成
2025-08-02 09:36:19 [WARN] 检测到Excel窗口句柄变化: 111876918 -> 7742166
2025-08-02 09:36:19 [INFO] SetTop: 父子窗口关系已正确 (Excel句柄: 7742166)
2025-08-02 09:36:19 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-08-02 09:36:19 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 7742166
2025-08-02 09:36:19 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 7742166)
2025-08-02 09:36:19 [INFO] TopMostForm.ForceRestoreParentRelation: 强制恢复完成
2025-08-02 09:36:19 [INFO] App_WorkbookActivate: TopForm关系验证完成
2025-08-02 09:36:19 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-08-02 09:36:19 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-08-02 09:36:19 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 7742166
2025-08-02 09:36:19 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 7742166)
2025-08-02 09:36:19 [INFO] TopMostForm.ForceRestoreParentRelation: 强制恢复完成
2025-08-02 09:36:19 [INFO] App_WorkbookActivate: TopForm关系验证完成
2025-08-02 09:36:19 [INFO] SetTop: 父子窗口关系已正确 (Excel句柄: 7742166)
2025-08-02 09:36:19 [INFO] TopMostForm.ForceRestoreParentRelation: 强制恢复完成
2025-08-02 09:36:19 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-08-02 09:36:19 [INFO] TopMostForm.Start: 开始启动窗体管理功能
2025-08-02 09:36:19 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 111876918
2025-08-02 09:36:19 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 111876918)
2025-08-02 09:36:19 [INFO] TopMostForm.Start: 窗体管理功能启动完成
2025-08-02 09:36:19 [INFO] App_WorkbookActivate: 工作簿 '场景清单_专用场景_ad60d545-8ea4-4b24-bd64-e2b8352ed1a1.csv' 激活处理完成
2025-08-02 09:36:19 [INFO] TopMostForm.Start: 开始启动窗体管理功能
2025-08-02 09:36:19 [INFO] SetTop: 父子窗口关系已正确 (Excel句柄: 111876918)
2025-08-02 09:36:19 [INFO] TopMostForm.Start: 窗体管理功能启动完成
2025-08-02 09:36:19 [INFO] App_WindowActivate: Excel窗口激活处理完成
2025-08-02 09:36:19 [INFO] SetTop: 父子窗口关系已正确 (Excel句柄: 111876918)
2025-08-02 09:36:19 [INFO] TopMostForm.ForceRestoreParentRelation: 强制恢复完成
2025-08-02 09:36:19 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-08-02 09:36:20 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 7742166
2025-08-02 09:36:20 [INFO] TopMostForm.Start: 开始启动窗体管理功能
2025-08-02 09:36:20 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 7742166
2025-08-02 09:36:20 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 7742166)
2025-08-02 09:36:20 [INFO] TopMostForm.Start: 窗体管理功能启动完成
2025-08-02 09:36:20 [INFO] App_WorkbookActivate: 工作簿 '一点一案上传方案信息表-20250801-170610.xlsx' 激活处理完成
2025-08-02 09:36:20 [INFO] TopMostForm.Start: 开始启动窗体管理功能
2025-08-02 09:36:20 [INFO] SetTop: 父子窗口关系已正确 (Excel句柄: 7742166)
2025-08-02 09:36:20 [INFO] TopMostForm.Start: 窗体管理功能启动完成
2025-08-02 09:36:20 [INFO] App_WindowActivate: Excel窗口激活处理完成
2025-08-02 09:36:20 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 7742166)
2025-08-02 09:36:20 [INFO] TopMostForm.ForceRestoreParentRelation: 强制恢复完成
2025-08-02 09:36:20 [INFO] App_WorkbookActivate: TopForm关系验证完成
2025-08-02 09:36:20 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-08-02 09:36:20 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 7742166
2025-08-02 09:36:20 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 7742166)
2025-08-02 09:36:20 [INFO] TopMostForm.ForceRestoreParentRelation: 强制恢复完成
2025-08-02 09:36:20 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-08-02 09:36:20 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 7742166
2025-08-02 09:36:20 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 7742166)
2025-08-02 09:36:20 [INFO] TopMostForm.ForceRestoreParentRelation: 强制恢复完成
2025-08-02 09:36:20 [INFO] App_WorkbookActivate: TopForm关系验证完成
2025-08-02 09:36:20 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-08-02 09:36:20 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 7742166
2025-08-02 09:36:20 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 7742166)
2025-08-02 09:36:20 [INFO] TopMostForm.ForceRestoreParentRelation: 强制恢复完成
2025-08-02 09:36:23 [INFO] 启动网络授权信息获取任务（后台更新）
2025-08-02 09:36:23 [INFO] 授权信息刷新成功，版本: 1.0, 颁发者: ExtensionsTools
2025-08-02 09:36:23 [INFO] OpenForm: 窗体标题已设置为类名 '批量查找'
2025-08-02 09:36:23 [INFO] OpenForm: 准备打开窗体 '批量查找'，位置: Right，单实例: True
2025-08-02 09:36:23 [INFO] 开始显示窗体 '批量查找'，位置模式: Right
2025-08-02 09:36:23 [INFO] 从Remote成功获取到网络授权信息
2025-08-02 09:36:23 [INFO] 网络授权信息已更新并触发回调
2025-08-02 09:36:23 [INFO] 网络授权信息已从 Network 更新
2025-08-02 09:36:23 [INFO] 授权版本: 1.0
2025-08-02 09:36:23 [INFO] 颁发者: ExtensionsTools
2025-08-02 09:36:23 [INFO] 用户数量: 3
2025-08-02 09:36:23 [INFO] 分组权限数量: 2
2025-08-02 09:36:23 [WARN] 配置文件中未找到用户组信息
2025-08-02 09:36:23 [INFO] 已重新设置用户组: []
2025-08-02 09:36:23 [INFO] 用户组信息已重新设置
2025-08-02 09:36:23 [INFO] 立即刷新权限缓存和UI界面
2025-08-02 09:36:23 [INFO] 开始强制刷新权限缓存和UI界面
2025-08-02 09:36:23 [DEBUG] 使用新的权限管理器进行强制刷新
2025-08-02 09:36:23 [DEBUG] 开始强制刷新HyExcel权限缓存和UI界面
2025-08-02 09:36:23 [INFO] 开始强制刷新权限缓存和UI界面
2025-08-02 09:36:23 [DEBUG] 本地权限缓存已清空
2025-08-02 09:36:23 [DEBUG] 跳过 LicenseController 刷新，避免死循环
2025-08-02 09:36:23 [INFO] 所有权限检查完成
2025-08-02 09:36:23 [DEBUG] 权限重新检查完成
2025-08-02 09:36:23 [DEBUG] 🔍 执行User权限UI操作 - znTab可见性: True
2025-08-02 09:36:23 [DEBUG] 🔍 执行User权限UI操作 - 用户有User权限，隐藏znAbout授权控件
2025-08-02 09:36:23 [DEBUG] 🔍 znAbout控件隐藏操作完成 - znAbout.Visible=false
2025-08-02 09:36:23 [DEBUG] 🔍 执行Develop权限UI操作 - hyTab可见性: True
2025-08-02 09:36:23 [DEBUG] 🔍 执行Develop权限UI操作 - 用户有Develop权限，隐藏znAbout授权控件
2025-08-02 09:36:23 [DEBUG] 🔍 znAbout控件隐藏操作完成 - znAbout.Visible=false
2025-08-02 09:36:23 [DEBUG] 已应用权限状态到UI控件
2025-08-02 09:36:23 [INFO] UI界面权限状态已更新
2025-08-02 09:36:23 [DEBUG] 开始刷新所有控件权限状态
2025-08-02 09:36:23 [DEBUG] 控件权限状态刷新完成，已检查 116 个控件
2025-08-02 09:36:23 [DEBUG] HyExcel权限缓存和UI界面强制刷新完成
2025-08-02 09:36:23 [INFO] 权限缓存和UI界面立即刷新完成
2025-08-02 09:36:23 [INFO] 网络授权已更新，开始刷新控件标题
2025-08-02 09:36:23 [INFO] 开始刷新Ribbon控件标题
2025-08-02 09:36:23 [DEBUG] 权限缓存已清空，清除了 116 个缓存项
2025-08-02 09:36:23 [DEBUG] 开始刷新HyExcel Ribbon控件标题
2025-08-02 09:36:23 [INFO] 开始动态更正控件标题（避免硬编码）
2025-08-02 09:36:23 [DEBUG] 开始动态获取Ribbon控件引用
2025-08-02 09:36:23 [DEBUG] 🔍 HyRibbon反射获取到 124 个字段
2025-08-02 09:36:23 [INFO] 🔍 HyRibbon中发现 3 个znAbout相关字段: [znAboutGroup, znAboutButton, znAbout]
2025-08-02 09:36:23 [INFO] 🔍 HyRibbon处理znAbout字段: znAboutGroup, 类型: Microsoft.Office.Tools.Ribbon.RibbonGroup
2025-08-02 09:36:23 [INFO] 🔍 znAbout字段 znAboutGroup 控件实例获取成功，已添加到引用字典
2025-08-02 09:36:23 [INFO] 🔍 HyRibbon处理znAbout字段: znAboutButton, 类型: Microsoft.Office.Tools.Ribbon.RibbonButton
2025-08-02 09:36:23 [INFO] 🔍 znAbout字段 znAboutButton 控件实例获取成功，已添加到引用字典
2025-08-02 09:36:23 [INFO] 🔍 HyRibbon处理znAbout字段: znAbout, 类型: Microsoft.Office.Tools.Ribbon.RibbonTab
2025-08-02 09:36:23 [INFO] 🔍 znAbout字段 znAbout 控件实例获取成功，已添加到引用字典
2025-08-02 09:36:23 [INFO] 动态获取到 122 个Ribbon控件引用
2025-08-02 09:36:23 [INFO] 🔍 HyRibbon最终控件引用中包含 3 个znAbout控件: [znAboutGroup, znAboutButton, znAbout]
2025-08-02 09:36:23 [INFO] 开始批量更新控件标题，共 122 个控件
2025-08-02 09:36:23 [DEBUG] 🔍 开始获取znAbout控件显示标题: znAboutGroup
2025-08-02 09:36:23 [DEBUG] 🔍 znAbout控件 znAboutGroup 从全局映射获取到正常标题: '授权'
2025-08-02 09:36:23 [DEBUG] 🔍 znAbout控件 znAboutGroup 权限检查结果: True
2025-08-02 09:36:23 [DEBUG] 🔍 znAbout控件 znAboutGroup 有权限，返回正常标题: '授权'
2025-08-02 09:36:23 [DEBUG] 🔍 开始获取znAbout控件显示标题: znAboutButton
2025-08-02 09:36:23 [DEBUG] 🔍 znAbout控件 znAboutButton 从全局映射获取到正常标题: '授权'
2025-08-02 09:36:23 [DEBUG] 🔍 znAbout控件 znAboutButton 权限检查结果: True
2025-08-02 09:36:23 [DEBUG] 🔍 znAbout控件 znAboutButton 有权限，返回正常标题: '授权'
2025-08-02 09:36:23 [DEBUG] 🔍 开始获取znAbout控件显示标题: znAbout
2025-08-02 09:36:23 [DEBUG] 🔍 znAbout控件 znAbout 从全局映射获取到正常标题: 'ZnAbout'
2025-08-02 09:36:23 [DEBUG] 🔍 znAbout控件 znAbout 权限检查结果: True
2025-08-02 09:36:23 [DEBUG] 🔍 znAbout控件 znAbout 有权限，返回正常标题: 'ZnAbout'
2025-08-02 09:36:23 [INFO] 批量更新控件标题完成，成功更新 109 个控件
2025-08-02 09:36:23 [INFO] 动态批量更新完成，共更新 122 个控件
2025-08-02 09:36:23 [INFO] 控件标题更正完成
2025-08-02 09:36:23 [DEBUG] HyExcel Ribbon控件标题刷新完成
2025-08-02 09:36:23 [INFO] Ribbon控件标题刷新完成
2025-08-02 09:36:23 [INFO] 控件标题刷新完成
2025-08-02 09:36:23 [DEBUG] Ribbon控件标题已刷新
2025-08-02 09:36:23 [INFO] 开始刷新控件标题
2025-08-02 09:36:23 [DEBUG] 开始刷新所有控件权限状态
2025-08-02 09:36:23 [DEBUG] 控件权限状态刷新完成，已检查 116 个控件
2025-08-02 09:36:23 [DEBUG] 控件标题刷新完成
2025-08-02 09:36:23 [INFO] 开始动态更正控件标题（避免硬编码）
2025-08-02 09:36:23 [DEBUG] 开始动态获取Ribbon控件引用
2025-08-02 09:36:23 [DEBUG] 🔍 HyRibbon反射获取到 124 个字段
2025-08-02 09:36:23 [INFO] 🔍 HyRibbon中发现 3 个znAbout相关字段: [znAboutGroup, znAboutButton, znAbout]
2025-08-02 09:36:23 [INFO] 🔍 HyRibbon处理znAbout字段: znAboutGroup, 类型: Microsoft.Office.Tools.Ribbon.RibbonGroup
2025-08-02 09:36:23 [INFO] 🔍 znAbout字段 znAboutGroup 控件实例获取成功，已添加到引用字典
2025-08-02 09:36:23 [INFO] 🔍 HyRibbon处理znAbout字段: znAboutButton, 类型: Microsoft.Office.Tools.Ribbon.RibbonButton
2025-08-02 09:36:23 [INFO] 🔍 znAbout字段 znAboutButton 控件实例获取成功，已添加到引用字典
2025-08-02 09:36:23 [INFO] 🔍 HyRibbon处理znAbout字段: znAbout, 类型: Microsoft.Office.Tools.Ribbon.RibbonTab
2025-08-02 09:36:23 [INFO] 🔍 znAbout字段 znAbout 控件实例获取成功，已添加到引用字典
2025-08-02 09:36:23 [INFO] 动态获取到 122 个Ribbon控件引用
2025-08-02 09:36:23 [INFO] 🔍 HyRibbon最终控件引用中包含 3 个znAbout控件: [znAboutGroup, znAboutButton, znAbout]
2025-08-02 09:36:23 [INFO] 开始批量更新控件标题，共 122 个控件
2025-08-02 09:36:23 [DEBUG] 🔍 开始获取znAbout控件显示标题: znAboutGroup
2025-08-02 09:36:23 [DEBUG] 🔍 znAbout控件 znAboutGroup 从全局映射获取到正常标题: '授权'
2025-08-02 09:36:23 [DEBUG] 🔍 znAbout控件 znAboutGroup 权限检查结果: True
2025-08-02 09:36:23 [DEBUG] 🔍 znAbout控件 znAboutGroup 有权限，返回正常标题: '授权'
2025-08-02 09:36:23 [DEBUG] 🔍 开始获取znAbout控件显示标题: znAboutButton
2025-08-02 09:36:23 [DEBUG] 🔍 znAbout控件 znAboutButton 从全局映射获取到正常标题: '授权'
2025-08-02 09:36:23 [DEBUG] 🔍 znAbout控件 znAboutButton 权限检查结果: True
2025-08-02 09:36:23 [DEBUG] 🔍 znAbout控件 znAboutButton 有权限，返回正常标题: '授权'
2025-08-02 09:36:23 [DEBUG] 🔍 开始获取znAbout控件显示标题: znAbout
2025-08-02 09:36:23 [DEBUG] 🔍 znAbout控件 znAbout 从全局映射获取到正常标题: 'ZnAbout'
2025-08-02 09:36:23 [DEBUG] 🔍 znAbout控件 znAbout 权限检查结果: True
2025-08-02 09:36:23 [DEBUG] 🔍 znAbout控件 znAbout 有权限，返回正常标题: 'ZnAbout'
2025-08-02 09:36:23 [INFO] 批量更新控件标题完成，成功更新 109 个控件
2025-08-02 09:36:23 [INFO] 动态批量更新完成，共更新 122 个控件
2025-08-02 09:36:23 [INFO] 控件标题更正完成
2025-08-02 09:36:23 [INFO] 控件标题刷新完成
2025-08-02 09:36:23 [DEBUG] Ribbon控件标题已立即刷新
2025-08-02 09:36:23 [INFO] 窗体 '批量查找' 以TopMostForm为父窗体显示
2025-08-02 09:36:23 [INFO] 窗体 '批量查找' 显示完成，句柄: 19533540
2025-08-02 09:36:23 [INFO] OpenForm: 窗体 '批量查找' 打开成功
2025-08-02 09:36:23 [DEBUG] 🔍 执行User权限UI操作 - znTab可见性: True
2025-08-02 09:36:23 [DEBUG] 🔍 执行User权限UI操作 - 用户有User权限，隐藏znAbout授权控件
2025-08-02 09:36:23 [DEBUG] 🔍 znAbout控件隐藏操作完成 - znAbout.Visible=false
2025-08-02 09:36:23 [DEBUG] 🔍 执行Develop权限UI操作 - hyTab可见性: True
2025-08-02 09:36:23 [DEBUG] 🔍 执行Develop权限UI操作 - 用户有Develop权限，隐藏znAbout授权控件
2025-08-02 09:36:23 [DEBUG] 🔍 znAbout控件隐藏操作完成 - znAbout.Visible=false
2025-08-02 09:36:23 [DEBUG] 已应用权限状态到UI控件
2025-08-02 09:36:23 [INFO] 开始刷新授权状态
2025-08-02 09:36:23 [DEBUG] 开始初始化授权验证
2025-08-02 09:36:23 [DEBUG] 使用新的权限管理器进行初始化
2025-08-02 09:36:23 [DEBUG] 开始初始化HyExcel UI权限管理器
2025-08-02 09:36:23 [INFO] 开始初始化UI权限管理
2025-08-02 09:36:23 [DEBUG] [实例ID: baf9753e] 永远有权限的特殊控件初始化完成，当前数量: 0
2025-08-02 09:36:23 [DEBUG] 🔍 [实例ID: baf9753e] 字典引用一致性检查:
2025-08-02 09:36:23 [DEBUG] 🔍   标题映射一致性: True
2025-08-02 09:36:23 [DEBUG] 🔍   权限映射一致性: True
2025-08-02 09:36:23 [DEBUG] 🔍   信息映射一致性: True
2025-08-02 09:36:23 [DEBUG] 🔍   特殊控件一致性: True
2025-08-02 09:36:23 [DEBUG] 控件权限管理器初始化完成 [实例ID: baf9753e]
2025-08-02 09:36:23 [DEBUG] 开始注册控件权限映射
2025-08-02 09:36:23 [DEBUG] 批量注册控件权限映射完成，成功: 116/116
2025-08-02 09:36:23 [DEBUG] HyExcel控件权限映射注册完成，共注册 116 个控件
2025-08-02 09:36:23 [INFO] 开始初始化权限验证
2025-08-02 09:36:23 [DEBUG] 设置默认UI可见性为false
2025-08-02 09:36:23 [DEBUG] 开始检查所有需要的权限
2025-08-02 09:36:23 [INFO] 所有权限检查完成
2025-08-02 09:36:23 [DEBUG] 应用权限状态到UI控件
2025-08-02 09:36:23 [DEBUG] 🔍 执行User权限UI操作 - znTab可见性: True
2025-08-02 09:36:23 [DEBUG] 🔍 执行User权限UI操作 - 用户有User权限，隐藏znAbout授权控件
2025-08-02 09:36:24 [DEBUG] 🔍 znAbout控件隐藏操作完成 - znAbout.Visible=false
2025-08-02 09:36:24 [DEBUG] 🔍 执行Develop权限UI操作 - hyTab可见性: True
2025-08-02 09:36:24 [DEBUG] 🔍 执行Develop权限UI操作 - 用户有Develop权限，隐藏znAbout授权控件
2025-08-02 09:36:24 [DEBUG] 🔍 znAbout控件隐藏操作完成 - znAbout.Visible=false
2025-08-02 09:36:24 [DEBUG] 已应用权限状态到UI控件
2025-08-02 09:36:24 [DEBUG] 启动后台权限刷新任务
2025-08-02 09:36:24 [DEBUG] 启动延迟权限刷新任务
2025-08-02 09:36:24 [INFO] 权限验证初始化完成
2025-08-02 09:36:24 [INFO] UI权限管理初始化完成
2025-08-02 09:36:24 [INFO] 收到权限管理器初始化完成通知
2025-08-02 09:36:24 [INFO] 开始刷新控件标题
2025-08-02 09:36:24 [DEBUG] 开始刷新所有控件权限状态
2025-08-02 09:36:24 [DEBUG] 🔍 执行User权限UI操作 - znTab可见性: True
2025-08-02 09:36:24 [DEBUG] 🔍 执行User权限UI操作 - 用户有User权限，隐藏znAbout授权控件
2025-08-02 09:36:24 [DEBUG] 🔍 znAbout控件隐藏操作完成 - znAbout.Visible=false
2025-08-02 09:36:24 [DEBUG] 🔍 执行Develop权限UI操作 - hyTab可见性: True
2025-08-02 09:36:24 [DEBUG] 🔍 执行Develop权限UI操作 - 用户有Develop权限，隐藏znAbout授权控件
2025-08-02 09:36:24 [DEBUG] 🔍 znAbout控件隐藏操作完成 - znAbout.Visible=false
2025-08-02 09:36:24 [DEBUG] 已应用权限状态到UI控件
2025-08-02 09:36:24 [DEBUG] 控件权限状态刷新完成，已检查 116 个控件
2025-08-02 09:36:24 [DEBUG] 控件标题刷新完成
2025-08-02 09:36:24 [INFO] 开始动态更正控件标题（避免硬编码）
2025-08-02 09:36:24 [DEBUG] 开始动态获取Ribbon控件引用
2025-08-02 09:36:24 [DEBUG] 🔍 HyRibbon反射获取到 124 个字段
2025-08-02 09:36:24 [INFO] 🔍 HyRibbon中发现 3 个znAbout相关字段: [znAboutGroup, znAboutButton, znAbout]
2025-08-02 09:36:24 [INFO] 🔍 HyRibbon处理znAbout字段: znAboutGroup, 类型: Microsoft.Office.Tools.Ribbon.RibbonGroup
2025-08-02 09:36:24 [INFO] 🔍 znAbout字段 znAboutGroup 控件实例获取成功，已添加到引用字典
2025-08-02 09:36:24 [INFO] 🔍 HyRibbon处理znAbout字段: znAboutButton, 类型: Microsoft.Office.Tools.Ribbon.RibbonButton
2025-08-02 09:36:24 [INFO] 🔍 znAbout字段 znAboutButton 控件实例获取成功，已添加到引用字典
2025-08-02 09:36:24 [INFO] 🔍 HyRibbon处理znAbout字段: znAbout, 类型: Microsoft.Office.Tools.Ribbon.RibbonTab
2025-08-02 09:36:24 [INFO] 🔍 znAbout字段 znAbout 控件实例获取成功，已添加到引用字典
2025-08-02 09:36:24 [INFO] 动态获取到 122 个Ribbon控件引用
2025-08-02 09:36:24 [INFO] 🔍 HyRibbon最终控件引用中包含 3 个znAbout控件: [znAboutGroup, znAboutButton, znAbout]
2025-08-02 09:36:24 [INFO] 开始批量更新控件标题，共 122 个控件
2025-08-02 09:36:24 [DEBUG] 🔍 开始获取znAbout控件显示标题: znAboutGroup
2025-08-02 09:36:24 [DEBUG] 🔍 znAbout控件 znAboutGroup 从全局映射获取到正常标题: '授权'
2025-08-02 09:36:24 [DEBUG] 🔍 znAbout控件 znAboutGroup 权限检查结果: True
2025-08-02 09:36:24 [DEBUG] 🔍 znAbout控件 znAboutGroup 有权限，返回正常标题: '授权'
2025-08-02 09:36:24 [DEBUG] 🔍 开始获取znAbout控件显示标题: znAboutButton
2025-08-02 09:36:24 [DEBUG] 🔍 znAbout控件 znAboutButton 从全局映射获取到正常标题: '授权'
2025-08-02 09:36:24 [DEBUG] 🔍 znAbout控件 znAboutButton 权限检查结果: True
2025-08-02 09:36:24 [DEBUG] 🔍 znAbout控件 znAboutButton 有权限，返回正常标题: '授权'
2025-08-02 09:36:24 [DEBUG] 🔍 开始获取znAbout控件显示标题: znAbout
2025-08-02 09:36:24 [DEBUG] 🔍 znAbout控件 znAbout 从全局映射获取到正常标题: 'ZnAbout'
2025-08-02 09:36:24 [DEBUG] 🔍 znAbout控件 znAbout 权限检查结果: True
2025-08-02 09:36:24 [DEBUG] 🔍 znAbout控件 znAbout 有权限，返回正常标题: 'ZnAbout'
2025-08-02 09:36:24 [INFO] 批量更新控件标题完成，成功更新 109 个控件
2025-08-02 09:36:24 [INFO] 动态批量更新完成，共更新 122 个控件
2025-08-02 09:36:24 [INFO] 控件标题更正完成
2025-08-02 09:36:24 [INFO] 控件标题刷新完成
2025-08-02 09:36:24 [INFO] 权限管理器初始化完成处理结束
2025-08-02 09:36:24 [DEBUG] HyExcel UI权限管理器初始化完成
2025-08-02 09:36:24 [DEBUG] 授权验证初始化完成
2025-08-02 09:36:24 [INFO] 授权状态刷新完成
2025-08-02 09:36:26 [DEBUG] 🔍 执行User权限UI操作 - znTab可见性: True
2025-08-02 09:36:26 [DEBUG] 🔍 执行User权限UI操作 - 用户有User权限，隐藏znAbout授权控件
2025-08-02 09:36:26 [DEBUG] 🔍 znAbout控件隐藏操作完成 - znAbout.Visible=false
2025-08-02 09:36:26 [DEBUG] 🔍 执行Develop权限UI操作 - hyTab可见性: True
2025-08-02 09:36:26 [DEBUG] 🔍 执行Develop权限UI操作 - 用户有Develop权限，隐藏znAbout授权控件
2025-08-02 09:36:26 [DEBUG] 🔍 znAbout控件隐藏操作完成 - znAbout.Visible=false
2025-08-02 09:36:26 [DEBUG] 已应用权限状态到UI控件
2025-08-02 09:36:29 [DEBUG] [ET.Controls.ETRangeSelectControl] 控件设置初始化完成
2025-08-02 09:36:29 [DEBUG] [ET.Controls.ETRangeSelectControl] ETRangeSelectControl初始化完成，使用WPS直接提供者
2025-08-02 09:36:29 [DEBUG] [ET.Controls.ETRangeSelectControl] 控件设置初始化完成
2025-08-02 09:36:29 [DEBUG] [ET.Controls.ETRangeSelectControl] ETRangeSelectControl初始化完成，使用WPS直接提供者
2025-08-02 09:36:29 [DEBUG] [ET.Controls.ETRangeSelectControl] 控件设置初始化完成
2025-08-02 09:36:29 [DEBUG] [ET.Controls.ETRangeSelectControl] ETRangeSelectControl初始化完成，使用WPS直接提供者
2025-08-02 09:36:29 [DEBUG] [ET.Controls.ETRangeSelectControl] 控件设置初始化完成
2025-08-02 09:36:29 [DEBUG] [ET.Controls.ETRangeSelectControl] ETRangeSelectControl初始化完成，使用WPS直接提供者
2025-08-02 09:36:29 [DEBUG] [ET.Controls.ETRangeSelectControl] 控件设置初始化完成
2025-08-02 09:36:29 [DEBUG] [ET.Controls.ETRangeSelectControl] ETRangeSelectControl初始化完成，使用WPS直接提供者
2025-08-02 09:36:29 [DEBUG] [ET.Controls.ETRangeSelectControl] 控件设置初始化完成
2025-08-02 09:36:29 [DEBUG] [ET.Controls.ETRangeSelectControl] ETRangeSelectControl初始化完成，使用WPS直接提供者
2025-08-02 09:36:29 [DEBUG] [ET.Controls.ETRangeSelectControl] 控件设置初始化完成
2025-08-02 09:36:29 [DEBUG] [ET.Controls.ETRangeSelectControl] ETRangeSelectControl初始化完成，使用WPS直接提供者
2025-08-02 09:36:29 [DEBUG] [ET.Controls.ETRangeSelectControl] 控件设置初始化完成
2025-08-02 09:36:29 [DEBUG] [ET.Controls.ETRangeSelectControl] ETRangeSelectControl初始化完成，使用WPS直接提供者
2025-08-02 09:36:29 [DEBUG] [ET.Controls.ETRangeSelectControl] 控件设置初始化完成
2025-08-02 09:36:29 [DEBUG] [ET.Controls.ETRangeSelectControl] ETRangeSelectControl初始化完成，使用WPS直接提供者
2025-08-02 09:36:29 [DEBUG] [ET.Controls.ETRangeSelectControl] 控件设置初始化完成
2025-08-02 09:36:29 [DEBUG] [ET.Controls.ETRangeSelectControl] ETRangeSelectControl初始化完成，使用WPS直接提供者
2025-08-02 09:36:29 [DEBUG] [ET.Controls.ETRangeSelectControl] 控件设置初始化完成
2025-08-02 09:36:29 [DEBUG] [ET.Controls.ETRangeSelectControl] ETRangeSelectControl初始化完成，使用WPS直接提供者
2025-08-02 09:36:29 [DEBUG] [ET.Controls.ETRangeSelectControl] 控件设置初始化完成
2025-08-02 09:36:29 [DEBUG] [ET.Controls.ETRangeSelectControl] ETRangeSelectControl初始化完成，使用WPS直接提供者
2025-08-02 09:36:29 [DEBUG] [ET.Controls.ETRangeSelectControl] 内部触发SelectedEvent事件：null
2025-08-02 09:36:29 [DEBUG] [ET.Controls.ETRangeSelectControl] 设置选中范围：null，触发事件：True
2025-08-02 09:36:29 [DEBUG] [ET.Controls.ETRangeSelectControl] 内部触发SelectedEvent事件：null
2025-08-02 09:36:29 [DEBUG] [ET.Controls.ETRangeSelectControl] 设置选中范围：null，触发事件：True
2025-08-02 09:36:29 [DEBUG] [ET.Controls.ETRangeSelectControl] 内部触发SelectedEvent事件：null
2025-08-02 09:36:29 [DEBUG] [ET.Controls.ETRangeSelectControl] 设置选中范围：null，触发事件：True
2025-08-02 09:36:29 [DEBUG] [ET.Controls.ETRangeSelectControl] 内部触发SelectedEvent事件：null
2025-08-02 09:36:29 [DEBUG] [ET.Controls.ETRangeSelectControl] 设置选中范围：null，触发事件：True
2025-08-02 09:36:29 [DEBUG] [ET.Controls.ETRangeSelectControl] 内部触发SelectedEvent事件：null
2025-08-02 09:36:29 [DEBUG] [ET.Controls.ETRangeSelectControl] 设置选中范围：null，触发事件：True
2025-08-02 09:36:29 [DEBUG] [ET.Controls.ETRangeSelectControl] 内部触发SelectedEvent事件：null
2025-08-02 09:36:29 [DEBUG] [ET.Controls.ETRangeSelectControl] 设置选中范围：null，触发事件：True
2025-08-02 09:36:29 [DEBUG] [ET.Controls.ETRangeSelectControl] 内部触发SelectedEvent事件：null
2025-08-02 09:36:29 [DEBUG] [ET.Controls.ETRangeSelectControl] 设置选中范围：null，触发事件：True
2025-08-02 09:36:29 [DEBUG] [ET.Controls.ETRangeSelectControl] 内部触发SelectedEvent事件：null
2025-08-02 09:36:29 [DEBUG] [ET.Controls.ETRangeSelectControl] 设置选中范围：null，触发事件：True
2025-08-02 09:36:29 [DEBUG] [ET.Controls.ETRangeSelectControl] 内部触发SelectedEvent事件：null
2025-08-02 09:36:29 [DEBUG] [ET.Controls.ETRangeSelectControl] 设置选中范围：null，触发事件：True
2025-08-02 09:36:29 [DEBUG] [ET.Controls.ETRangeSelectControl] 内部触发SelectedEvent事件：null
2025-08-02 09:36:29 [DEBUG] [ET.Controls.ETRangeSelectControl] 设置选中范围：null，触发事件：True
2025-08-02 09:36:29 [DEBUG] [ET.Controls.ETRangeSelectControl] 内部触发SelectedEvent事件：null
2025-08-02 09:36:29 [DEBUG] [ET.Controls.ETRangeSelectControl] 设置选中范围：null，触发事件：True
2025-08-02 09:36:29 [DEBUG] [ET.Controls.ETRangeSelectControl] 内部触发SelectedEvent事件：null
2025-08-02 09:36:29 [DEBUG] [ET.Controls.ETRangeSelectControl] 设置选中范围：null，触发事件：True
2025-08-02 09:36:29 [INFO] OpenForm: 保持窗体现有标题 '通过经纬度查找'
2025-08-02 09:36:29 [INFO] OpenForm: 准备打开窗体 '通过经纬度查找'，位置: Center，单实例: False
2025-08-02 09:36:29 [INFO] 开始显示窗体 '通过经纬度查找'，位置模式: Center
2025-08-02 09:36:30 [INFO] 窗体 '通过经纬度查找' 以TopMostForm为父窗体显示
2025-08-02 09:36:30 [INFO] 窗体 '通过经纬度查找' 显示完成，句柄: 249431220
2025-08-02 09:36:30 [INFO] OpenForm: 窗体 '通过经纬度查找' 打开成功
2025-08-02 09:40:43 [INFO] TopMostForm.Start: 开始启动窗体管理功能
2025-08-02 09:40:43 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 7742166, 新父窗口: 111876918
2025-08-02 09:40:43 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 111876918)
2025-08-02 09:40:43 [INFO] TopMostForm.Start: 窗体管理功能启动完成
2025-08-02 09:40:43 [INFO] App_WorkbookActivate: 工作簿 '场景清单_专用场景_ad60d545-8ea4-4b24-bd64-e2b8352ed1a1.csv' 激活处理完成
2025-08-02 09:40:43 [INFO] TopMostForm.Start: 开始启动窗体管理功能
2025-08-02 09:40:43 [INFO] SetTop: 父子窗口关系已正确 (Excel句柄: 111876918)
2025-08-02 09:40:43 [INFO] TopMostForm.Start: 窗体管理功能启动完成
2025-08-02 09:40:43 [INFO] App_WindowActivate: Excel窗口激活处理完成
2025-08-02 09:40:43 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-08-02 09:40:43 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 111876918
2025-08-02 09:40:43 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 111876918)
2025-08-02 09:40:43 [INFO] TopMostForm.ForceRestoreParentRelation: 强制恢复完成
2025-08-02 09:40:43 [INFO] App_WorkbookActivate: TopForm关系验证完成
2025-08-02 09:40:44 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-08-02 09:40:44 [WARN] 父子窗口关系不正确. 当前父窗口: 65552, 期望父窗口: 111876918
2025-08-02 09:40:44 [WARN] 检测到父子窗口关系异常，尝试修复
2025-08-02 09:40:44 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 111876918
2025-08-02 09:40:44 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 111876918)
2025-08-02 09:40:44 [INFO] SetTop: 父子窗口关系已正确 (Excel句柄: 111876918)
2025-08-02 09:40:44 [INFO] TopMostForm.ForceRestoreParentRelation: 强制恢复完成
2025-08-02 09:40:44 [INFO] TopMostForm.Start: 开始启动窗体管理功能
2025-08-02 09:40:44 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 111876918, 新父窗口: 7742166
2025-08-02 09:40:44 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 7742166)
2025-08-02 09:40:44 [INFO] TopMostForm.Start: 窗体管理功能启动完成
2025-08-02 09:40:44 [INFO] App_WorkbookActivate: 工作簿 '一点一案上传方案信息表-20250801-170610.xlsx' 激活处理完成
2025-08-02 09:40:44 [INFO] TopMostForm.Start: 开始启动窗体管理功能
2025-08-02 09:40:44 [INFO] SetTop: 父子窗口关系已正确 (Excel句柄: 7742166)
2025-08-02 09:40:44 [INFO] TopMostForm.Start: 窗体管理功能启动完成
2025-08-02 09:40:44 [INFO] App_WindowActivate: Excel窗口激活处理完成
2025-08-02 09:40:44 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-08-02 09:40:44 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 7742166
2025-08-02 09:40:44 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 7742166)
2025-08-02 09:40:44 [INFO] TopMostForm.ForceRestoreParentRelation: 强制恢复完成
2025-08-02 09:40:44 [INFO] App_WorkbookActivate: TopForm关系验证完成
2025-08-02 09:40:44 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-08-02 09:40:44 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 7742166
2025-08-02 09:40:45 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 7742166)
2025-08-02 09:40:45 [INFO] TopMostForm.ForceRestoreParentRelation: 强制恢复完成
2025-08-02 10:27:08 [INFO] TopMostForm.Start: 开始启动窗体管理功能
2025-08-02 10:27:08 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 7742166, 新父窗口: 111876918
2025-08-02 10:27:08 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 111876918)
2025-08-02 10:27:08 [INFO] TopMostForm.Start: 窗体管理功能启动完成
2025-08-02 10:27:08 [INFO] App_WorkbookActivate: 工作簿 '场景清单_专用场景_ad60d545-8ea4-4b24-bd64-e2b8352ed1a1.csv' 激活处理完成
2025-08-02 10:27:08 [INFO] TopMostForm.Start: 开始启动窗体管理功能
2025-08-02 10:27:08 [INFO] SetTop: 父子窗口关系已正确 (Excel句柄: 111876918)
2025-08-02 10:27:08 [INFO] TopMostForm.Start: 窗体管理功能启动完成
2025-08-02 10:27:08 [INFO] App_WindowActivate: Excel窗口激活处理完成
2025-08-02 10:27:09 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-08-02 10:27:09 [WARN] 检测到Excel窗口句柄变化: 7742166 -> 111876918
2025-08-02 10:27:09 [INFO] SetTop: 父子窗口关系已正确 (Excel句柄: 111876918)
2025-08-02 10:27:09 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 111876918
2025-08-02 10:27:09 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 111876918)
2025-08-02 10:27:09 [INFO] TopMostForm.ForceRestoreParentRelation: 强制恢复完成
2025-08-02 10:27:09 [INFO] App_WorkbookActivate: TopForm关系验证完成
2025-08-02 10:27:09 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-08-02 10:27:09 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 111876918
2025-08-02 10:27:09 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 111876918)
2025-08-02 10:27:09 [INFO] TopMostForm.ForceRestoreParentRelation: 强制恢复完成
2025-08-02 15:20:02 [INFO] TopMostForm.Start: 开始启动窗体管理功能
2025-08-02 15:20:02 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 111876918, 新父窗口: 7742166
2025-08-02 15:20:02 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 7742166)
2025-08-02 15:20:02 [INFO] TopMostForm.Start: 窗体管理功能启动完成
2025-08-02 15:20:02 [INFO] App_WorkbookActivate: 工作簿 '一点一案上传方案信息表-20250801-170610.xlsx' 激活处理完成
2025-08-02 15:20:02 [INFO] TopMostForm.Start: 开始启动窗体管理功能
2025-08-02 15:20:02 [INFO] SetTop: 父子窗口关系已正确 (Excel句柄: 7742166)
2025-08-02 15:20:02 [INFO] TopMostForm.Start: 窗体管理功能启动完成
2025-08-02 15:20:02 [INFO] App_WindowActivate: Excel窗口激活处理完成
2025-08-02 15:20:03 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-08-02 15:20:03 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 7742166
2025-08-02 15:20:03 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-08-02 15:20:03 [ERROR] SetTop: 设置父窗口关系失败 - 期望: 7742166, 实际: 65552
2025-08-02 15:20:03 [INFO] TopMostForm.ForceRestoreParentRelation: 强制恢复完成
2025-08-02 15:20:03 [INFO] App_WorkbookActivate: TopForm关系验证完成
2025-08-02 15:20:03 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 7742166
2025-08-02 15:20:03 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 7742166)
2025-08-02 15:20:03 [INFO] TopMostForm.ForceRestoreParentRelation: 强制恢复完成
