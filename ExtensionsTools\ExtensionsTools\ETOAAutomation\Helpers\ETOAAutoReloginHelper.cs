using System;
using System.Threading.Tasks;
using ExtensionsTools.ETOAAutomation.Models;
using ExtensionsTools.ETOAAutomation.Storage;
using ET;

namespace ExtensionsTools.ETOAAutomation.Helpers
{
    /// <summary>
    /// 自动重登辅助类，处理会话过期后的自动重新登录功能
    /// </summary>
    public class ETOAAutoReloginHelper
    {
        #region 私有字段
        private readonly ETOAApiClient _apiClient;
        private readonly ETOAAuthStorage _authStorage;
        private readonly ETOASessionManager _sessionManager;
        private string _lastUsername;
        private string _lastPassword;
        private string _baseUrl;
        private bool _isReloginInProgress = false;
        private readonly object _reloginLock = new object();
        #endregion

        #region 公共属性
        /// <summary>
        /// 是否正在重登过程中
        /// </summary>
        public bool IsReloginInProgress => _isReloginInProgress;

        /// <summary>
        /// 最后使用的用户名
        /// </summary>
        public string LastUsername => _lastUsername;

        /// <summary>
        /// OA系统基础URL
        /// </summary>
        public string BaseUrl 
        { 
            get => _baseUrl; 
            set => _baseUrl = value; 
        }
        #endregion

        #region 事件定义
        /// <summary>
        /// 重登开始事件
        /// </summary>
        public event EventHandler<ReloginStartedEventArgs> ReloginStarted;

        /// <summary>
        /// 重登完成事件
        /// </summary>
        public event EventHandler<ReloginCompletedEventArgs> ReloginCompleted;

        /// <summary>
        /// 重登失败事件
        /// </summary>
        public event EventHandler<ReloginFailedEventArgs> ReloginFailed;
        #endregion

        #region 构造函数
        /// <summary>
        /// 初始化自动重登辅助类
        /// </summary>
        /// <param name="apiClient">API客户端</param>
        /// <param name="sessionManager">会话管理器</param>
        /// <param name="baseUrl">OA系统基础URL</param>
        public ETOAAutoReloginHelper(ETOAApiClient apiClient, ETOASessionManager sessionManager, string baseUrl)
        {
            _apiClient = apiClient ?? throw new ArgumentNullException(nameof(apiClient));
            _sessionManager = sessionManager ?? throw new ArgumentNullException(nameof(sessionManager));
            _baseUrl = baseUrl ?? throw new ArgumentNullException(nameof(baseUrl));
            _authStorage = new ETOAAuthStorage();

            ETLogManager.Info("ETOAAutoReloginHelper初始化完成");
        }
        #endregion

        #region 公共方法
        /// <summary>
        /// 设置登录凭据
        /// </summary>
        /// <param name="username">用户名</param>
        /// <param name="password">密码</param>
        public void SetCredentials(string username, string password)
        {
            try
            {
                _lastUsername = username;
                _lastPassword = password;

                ETLogManager.Info($"自动重登凭据设置完成，用户: {username}");
            }
            catch (Exception ex)
            {
                ETLogManager.Error("设置自动重登凭据失败", ex);
                throw new ETException("设置自动重登凭据失败", "ETOAAutoReloginHelper.SetCredentials", ex);
            }
        }

        /// <summary>
        /// 执行自动重登
        /// </summary>
        /// <param name="attemptNumber">尝试次数</param>
        /// <returns>重登是否成功</returns>
        public async Task<bool> ExecuteAutoReloginAsync(int attemptNumber = 1)
        {
            lock (_reloginLock)
            {
                if (_isReloginInProgress)
                {
                    ETLogManager.Warn("自动重登已在进行中，跳过本次请求");
                    return false;
                }
                _isReloginInProgress = true;
            }

            try
            {
                ETLogManager.Info($"开始执行自动重登，第 {attemptNumber} 次尝试");

                // 触发重登开始事件
                OnReloginStarted(new ReloginStartedEventArgs 
                { 
                    AttemptNumber = attemptNumber,
                    Username = _lastUsername
                });

                // 检查是否有保存的凭据
                if (string.IsNullOrEmpty(_lastUsername) || string.IsNullOrEmpty(_lastPassword))
                {
                    // 尝试从存储中加载凭据
                    var savedAuth = _authStorage.LoadAuthInfo(_lastUsername ?? "");
                    if (savedAuth != null && savedAuth.IsSuccess)
                    {
                        _lastUsername = savedAuth.Username;
                        // 注意：密码通常不会保存，这里需要其他方式获取
                        ETLogManager.Info("从存储中恢复了认证信息");
                    }
                    else
                    {
                        ETLogManager.Error("无法获取登录凭据，自动重登失败");
                        OnReloginFailed(new ReloginFailedEventArgs 
                        { 
                            AttemptNumber = attemptNumber,
                            Reason = "缺少登录凭据",
                            Username = _lastUsername
                        });
                        return false;
                    }
                }

                // 执行登录操作
                var loginResult = await PerformLoginAsync();

                if (loginResult.Success)
                {
                    ETLogManager.Info($"自动重登成功，第 {attemptNumber} 次尝试");
                    
                    // 更新API客户端认证信息
                    _apiClient.SetAuthenticationInfo(loginResult.LoginInfo);
                    
                    // 重新初始化会话管理
                    _sessionManager.InitializeSession(_lastUsername, _lastPassword, loginResult.SessionData);
                    
                    // 触发重登完成事件
                    OnReloginCompleted(new ReloginCompletedEventArgs 
                    { 
                        Success = true,
                        AttemptNumber = attemptNumber,
                        Username = _lastUsername,
                        LoginInfo = loginResult.LoginInfo
                    });

                    return true;
                }
                else
                {
                    ETLogManager.Error($"自动重登失败，第 {attemptNumber} 次尝试：{loginResult.ErrorMessage}");
                    
                    OnReloginFailed(new ReloginFailedEventArgs 
                    { 
                        AttemptNumber = attemptNumber,
                        Reason = loginResult.ErrorMessage,
                        Username = _lastUsername,
                        Exception = loginResult.Exception
                    });

                    return false;
                }
            }
            catch (Exception ex)
            {
                ETLogManager.Error($"自动重登过程中发生异常，第 {attemptNumber} 次尝试", ex);
                
                OnReloginFailed(new ReloginFailedEventArgs 
                { 
                    AttemptNumber = attemptNumber,
                    Reason = ex.Message,
                    Username = _lastUsername,
                    Exception = ex
                });

                return false;
            }
            finally
            {
                lock (_reloginLock)
                {
                    _isReloginInProgress = false;
                }
            }
        }

        /// <summary>
        /// 检查是否可以执行自动重登
        /// </summary>
        /// <returns>是否可以执行自动重登</returns>
        public bool CanExecuteAutoRelogin()
        {
            try
            {
                // 检查是否正在重登过程中
                if (_isReloginInProgress)
                {
                    return false;
                }

                // 检查是否有必要的凭据
                if (string.IsNullOrEmpty(_lastUsername))
                {
                    return false;
                }

                // 检查是否有保存的认证信息
                var hasStoredAuth = _authStorage.HasAuthInfo(_lastUsername);
                
                return hasStoredAuth || !string.IsNullOrEmpty(_lastPassword);
            }
            catch (Exception ex)
            {
                ETLogManager.Error("检查自动重登条件时发生错误", ex);
                return false;
            }
        }

        /// <summary>
        /// 清除保存的凭据
        /// </summary>
        public void ClearCredentials()
        {
            try
            {
                _lastUsername = null;
                _lastPassword = null;
                
                ETLogManager.Info("自动重登凭据已清除");
            }
            catch (Exception ex)
            {
                ETLogManager.Error("清除自动重登凭据时发生错误", ex);
            }
        }
        #endregion

        #region 私有方法
        /// <summary>
        /// 执行登录操作
        /// </summary>
        /// <returns>登录结果</returns>
        private async Task<LoginResult> PerformLoginAsync()
        {
            try
            {
                ETLogManager.Debug("开始执行登录操作");

                // 创建登录浏览器
                using (var loginBrowser = new ETOALoginBrowser($"{_baseUrl}/login"))
                {
                    // 尝试自动登录
                    var success = await loginBrowser.AutoLoginAsync(_lastUsername, _lastPassword);
                    
                    if (success)
                    {
                        var loginInfo = loginBrowser.GetLoginInfo();
                        
                        // 创建会话数据
                        var sessionData = new ETOASessionData
                        {
                            Username = _lastUsername,
                            UserId = loginInfo.UserId,
                            AuthToken = loginInfo.Token,
                            Cookies = loginInfo.Cookies,
                            CreatedTime = DateTime.Now,
                            LastActivityTime = DateTime.Now
                        };

                        return new LoginResult
                        {
                            Success = true,
                            LoginInfo = loginInfo,
                            SessionData = sessionData
                        };
                    }
                    else
                    {
                        return new LoginResult
                        {
                            Success = false,
                            ErrorMessage = "登录浏览器返回失败"
                        };
                    }
                }
            }
            catch (Exception ex)
            {
                return new LoginResult
                {
                    Success = false,
                    ErrorMessage = ex.Message,
                    Exception = ex
                };
            }
        }

        /// <summary>
        /// 触发重登开始事件
        /// </summary>
        private void OnReloginStarted(ReloginStartedEventArgs e)
        {
            ReloginStarted?.Invoke(this, e);
        }

        /// <summary>
        /// 触发重登完成事件
        /// </summary>
        private void OnReloginCompleted(ReloginCompletedEventArgs e)
        {
            ReloginCompleted?.Invoke(this, e);
        }

        /// <summary>
        /// 触发重登失败事件
        /// </summary>
        private void OnReloginFailed(ReloginFailedEventArgs e)
        {
            ReloginFailed?.Invoke(this, e);
        }
        #endregion
    }

    #region 事件参数类和结果类
    /// <summary>
    /// 重登开始事件参数
    /// </summary>
    public class ReloginStartedEventArgs : EventArgs
    {
        /// <summary>
        /// 尝试次数
        /// </summary>
        public int AttemptNumber { get; set; }

        /// <summary>
        /// 用户名
        /// </summary>
        public string Username { get; set; }

        /// <summary>
        /// 开始时间
        /// </summary>
        public DateTime Timestamp { get; set; } = DateTime.Now;
    }

    /// <summary>
    /// 重登完成事件参数
    /// </summary>
    public class ReloginCompletedEventArgs : EventArgs
    {
        /// <summary>
        /// 是否成功
        /// </summary>
        public bool Success { get; set; }

        /// <summary>
        /// 尝试次数
        /// </summary>
        public int AttemptNumber { get; set; }

        /// <summary>
        /// 用户名
        /// </summary>
        public string Username { get; set; }

        /// <summary>
        /// 登录信息
        /// </summary>
        public ETOALoginInfo LoginInfo { get; set; }

        /// <summary>
        /// 完成时间
        /// </summary>
        public DateTime Timestamp { get; set; } = DateTime.Now;
    }

    /// <summary>
    /// 重登失败事件参数
    /// </summary>
    public class ReloginFailedEventArgs : EventArgs
    {
        /// <summary>
        /// 尝试次数
        /// </summary>
        public int AttemptNumber { get; set; }

        /// <summary>
        /// 失败原因
        /// </summary>
        public string Reason { get; set; }

        /// <summary>
        /// 用户名
        /// </summary>
        public string Username { get; set; }

        /// <summary>
        /// 异常信息
        /// </summary>
        public Exception Exception { get; set; }

        /// <summary>
        /// 失败时间
        /// </summary>
        public DateTime Timestamp { get; set; } = DateTime.Now;
    }

    /// <summary>
    /// 登录结果
    /// </summary>
    public class LoginResult
    {
        /// <summary>
        /// 是否成功
        /// </summary>
        public bool Success { get; set; }

        /// <summary>
        /// 登录信息
        /// </summary>
        public ETOALoginInfo LoginInfo { get; set; }

        /// <summary>
        /// 会话数据
        /// </summary>
        public ETOASessionData SessionData { get; set; }

        /// <summary>
        /// 错误信息
        /// </summary>
        public string ErrorMessage { get; set; }

        /// <summary>
        /// 异常信息
        /// </summary>
        public Exception Exception { get; set; }
    }
    #endregion
